Package: vcpkg-cmake-config
Version: 2022-02-06
Port-Version: 1
Architecture: x64-linux
Multi-Arch: same
Abi: df38c9b19cc47b36ee4a13a2ea0e1221d91f48e14a53abd74fc85a1d932e8769
Status: install ok installed

Package: vcpkg-cmake
Version: 2023-05-04
Architecture: x64-linux
Multi-Arch: same
Abi: 5d63033154ac260e035cc8e4ca023523cb793080536245442ffc3a0761ebb458
Status: install ok installed

Package: cargs
Version: 1.1.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 864c95f9672ba8f9c38f62ece064c3f966ae7cc8ccc35c2a93e916958f4db5e8
Description: A lightweight cross-platform getopt alternative that works on Linux, Windows and macOS. Command line argument parser library for C/C++. Can be used to parse argv and argc parameters.
Status: install ok installed

Package: libhv
Version: 1.3.2
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 082ca8d6b658e7f80c236bc7f97ab68b2939f2a27a347979cf9e948cafe1892a
Description: Libhv is a C/C++ network library similar to libevent/libuv.
Status: install ok installed

