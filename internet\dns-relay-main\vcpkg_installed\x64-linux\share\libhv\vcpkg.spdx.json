{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/libhv-x64-linux-1.3.2-b81d7ddd-b8ab-4f8e-9fc1-0a02bfea41b9", "name": "libhv:x64-linux@1.3.2 082ca8d6b658e7f80c236bc7f97ab68b2939f2a27a347979cf9e948cafe1892a", "creationInfo": {"creators": ["Tool: vcpkg-2025-05-19-ece4c0f6b8fae9e94513d544c7aa753dd2c82337"], "created": "2025-06-01T16:47:12Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "libhv", "SPDXID": "SPDXRef-port", "versionInfo": "1.3.2", "downloadLocation": "git+https://github.com/microsoft/vcpkg@f76c5430e5899a88a85fa7366d046310882f9223", "homepage": "https://github.com/ithewei/libhv", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Libhv is a C/C++ network library similar to libevent/libuv.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "libhv:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "082ca8d6b658e7f80c236bc7f97ab68b2939f2a27a347979cf9e948cafe1892a", "downloadLocation": "NONE", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "ithewei/libhv", "downloadLocation": "git+https://github.com/ithewei/libhv@v1.3.2", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "9dffb6e844df8ba825df88ef984c280923fbf1d50edcbbbe0b36927172ad82c057d65b9b752163c3e2383eb44db0261fd4e3623e3630a55a3f8987088bef0bd7"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "9349a6da4af53465b06f54e7e3d04b947b34e981cb44c4587b6faf6249175a57"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "c3e20af3a059b89d6030472c55d48148f7e9b829da55c778615bc17451b1d0f6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}