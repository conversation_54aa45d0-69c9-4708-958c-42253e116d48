{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/cargs-x64-linux-1.1.0-38fac8e8-3724-49e5-9b4c-07b9335fe14a", "name": "cargs:x64-linux@1.1.0 864c95f9672ba8f9c38f62ece064c3f966ae7cc8ccc35c2a93e916958f4db5e8", "creationInfo": {"creators": ["Tool: vcpkg-2025-05-19-ece4c0f6b8fae9e94513d544c7aa753dd2c82337"], "created": "2025-06-01T16:46:10Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "cargs", "SPDXID": "SPDXRef-port", "versionInfo": "1.1.0", "downloadLocation": "git+https://github.com/microsoft/vcpkg@88fea433a3287ccd49ae6a5bb168071418fa4469", "homepage": "https://likle.github.io/cargs/", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A lightweight cross-platform getopt alternative that works on Linux, Windows and macOS. Command line argument parser library for C/C++. Can be used to parse argv and argc parameters.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "cargs:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "864c95f9672ba8f9c38f62ece064c3f966ae7cc8ccc35c2a93e916958f4db5e8", "downloadLocation": "NONE", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "likle/cargs", "downloadLocation": "git+https://github.com/likle/cargs@v1.1.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "936fa94da31b07de27c0278688199705f9fdc55cf248c7a88405c373e5c77eed2a703d9398d3ea80a3a534db3d542898babb49db268d26c5945c4907540ccc1b"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7b8a260773cab12f45ee8a1851b27bdef6674e5c26b113565039a4dfe23db178"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "18d3be5556539fe281366f3cde6ed41476fbd2f9b7851d80d6fdabe51174f924"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}