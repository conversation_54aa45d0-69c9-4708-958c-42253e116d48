#pragma once  // 防止头文件被重复包含

#include "cargs.h"  // 包含命令行参数解析库 cargs 的头文件

/**
 * dns-relay 所有可用的命令行选项
 */
static struct cag_option options[] = {  // 定义静态数组，包含所有命令行选项的定义，定义了所有支持的命令行选项，包括短选项名、长选项名、是否需要参数以及描述信息
        {.identifier = 'd',  // 选项的唯一标识符，用于在代码中识别该选项
                .access_letters = "d",  // 短选项名，使用时格式为 -d
                .access_name = "debug",  // 长选项名，使用时格式为 --debug
                .value_name = NULL,  // 该选项不需要额外的值参数
                .description = "调试级别 1 (仅输出时间坐标，序号，查询的域名)"},  // 选项的描述文本，用于帮助信息

        {.identifier = 'm',  // 选项的唯一标识符
                .access_letters = "v",  // 短选项名为 -v
                .access_name = "verbose",  // 长选项名为 --verbose
                .value_name = NULL,  // 该选项不需要额外的值参数
                .description = "调试级别 2 (输出冗长的调试信息)"},  // 选项的描述文本

        {.identifier = 's',  // 选项的唯一标识符
                .access_letters = "s",  // 短选项名为 -s
                .access_name = "server",  // 长选项名为 --server
                .value_name = "dns-server-ipaddr",  // 该选项需要一个值参数，表示DNS服务器IP地址
                .description = "使用指定的 DNS 服务器"},  // 选项的描述文本

        {.identifier = 'f',  // 选项的唯一标识符
                .access_letters = "f",  // 短选项名为 -f
                .access_name = "filename",  // 长选项名为 --filename
                .value_name = "filename",  // 该选项需要一个值参数，表示配置文件名
                .description = "使用指定的配置文件 (默认为 dnsrelay.txt)"},  // 选项的描述文本

        {.identifier = 'p',  // 选项的唯一标识符
                .access_letters = "p",  // 短选项名为 -p
                .access_name = "port",  // 长选项名为 --port
                .value_name = "port",  // 该选项需要一个值参数，表示端口号
                .description = "使用指定的端口号 (默认为53)"},  // 选项的描述文本

        {.identifier = 't',  // 选项的唯一标识符
                .access_letters = "t",  // 短选项名为 -t
                .access_name = "timeout",  // 长选项名为 --timeout
                .value_name = "timeout"},  // 该选项需要一个值参数，表示超时时间，但没有提供描述

        {.identifier = 'c',  // 选项的唯一标识符
                .access_letters = "c",  // 短选项名为 -c
                .access_name = "cache",  // 长选项名为 --cache
                .value_name = "cache",  // 该选项需要一个值参数，表示缓存大小
                .description = "指定 Cache 最大数量 (默认为 2048)"},  // 选项的描述文本

        {
                .identifier = 'h',  // 选项的唯一标识符
                .access_letters = "h",  // 短选项名为 -h
                .access_name = "help",  // 长选项名为 --help
                .description = "显示本帮助信息，然后退出"}  // 选项的描述文本，该选项不需要额外的值参数
};

/**
 * dns-relay 的命令行参数信息
 */
struct Config {  // 定义配置结构体，用于存储解析后的命令行参数
    int debug_level,  // 调试级别：0-无调试信息，1-基本信息，2-详细信息
        port,         // 监听端口号
        cache_size,   // 缓存大小
        rto;          // 请求超时时间(Request Timeout)，单位为毫秒
    const char *dns_server_ipaddr;  // 上游DNS服务器的IP地址
    const char *filename;           // 配置文件名
};

/**
 * 解析命令行参数
 * @param argc 命令行参数个数
 * @param argv 命令行参数
 * @param config 解析结果的指针
 * @return 解析成功或失败，成功为 0，失败为 -1
 */
int parse_args(int argc, char **argv, struct Config *config);  // 声明解析命令行参数的函数

/**
 * 打印命令行参数
 * @param config 命令行参数信息
 */
void dump_args(struct Config *config);  // 声明打印配置信息的函数