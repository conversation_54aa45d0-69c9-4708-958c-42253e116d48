/**
 * @file args.h
 * @brief DNS中继服务器命令行参数处理头文件 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件定义了DNS中继服务器的命令行参数结构和解析函数，
 * 使用cargs库实现灵活的命令行参数处理功能。
 */

#pragma once  // 防止头文件被重复包含

#include "cargs.h"  // 包含命令行参数解析库 cargs 的头文件

/**
 * @brief DNS中继服务器所有可用的命令行选项定义
 *
 * 该数组定义了所有支持的命令行选项，包括：
 * - 短选项名（如 -d）
 * - 长选项名（如 --debug）
 * - 是否需要参数值
 * - 选项描述信息
 */
static struct cag_option options[] = {
        {
                .identifier = 'd',                    // 选项的唯一标识符，用于在代码中识别该选项
                .access_letters = "d",                // 短选项名，使用时格式为 -d
                .access_name = "debug",               // 长选项名，使用时格式为 --debug
                .value_name = NULL,                   // 该选项不需要额外的值参数
                .description = "调试级别 1 (仅输出时间坐标，序号，查询的域名)"  // 选项的描述文本
        },

        {
                .identifier = 'm',                    // 选项的唯一标识符（注意：内部使用'm'，但对外是'v'）
                .access_letters = "v",                // 短选项名为 -v
                .access_name = "verbose",             // 长选项名为 --verbose
                .value_name = NULL,                   // 该选项不需要额外的值参数
                .description = "调试级别 2 (输出冗长的调试信息)"  // 选项的描述文本
        },

        {
                .identifier = 's',                    // 选项的唯一标识符
                .access_letters = "s",                // 短选项名为 -s
                .access_name = "server",              // 长选项名为 --server
                .value_name = "dns-server-ipaddr",    // 该选项需要一个值参数，表示DNS服务器IP地址
                .description = "使用指定的 DNS 服务器"  // 选项的描述文本
        },

        {
                .identifier = 'f',                    // 选项的唯一标识符
                .access_letters = "f",                // 短选项名为 -f
                .access_name = "filename",            // 长选项名为 --filename
                .value_name = "filename",             // 该选项需要一个值参数，表示配置文件名
                .description = "使用指定的配置文件 (默认为 dnsrelay.txt)"  // 选项的描述文本
        },

        {
                .identifier = 'p',                    // 选项的唯一标识符
                .access_letters = "p",                // 短选项名为 -p
                .access_name = "port",                // 长选项名为 --port
                .value_name = "port",                 // 该选项需要一个值参数，表示端口号
                .description = "使用指定的端口号 (默认为53)"  // 选项的描述文本
        },

        {
                .identifier = 't',                    // 选项的唯一标识符
                .access_letters = "t",                // 短选项名为 -t
                .access_name = "timeout",             // 长选项名为 --timeout
                .value_name = "timeout",              // 该选项需要一个值参数，表示超时时间（毫秒）
                .description = "指定请求上级DNS服务器超时时间 (默认为5000ms)"  // 选项的描述文本
        },

        {
                .identifier = 'c',                    // 选项的唯一标识符
                .access_letters = "c",                // 短选项名为 -c
                .access_name = "cache",               // 长选项名为 --cache
                .value_name = "cache",                // 该选项需要一个值参数，表示缓存大小
                .description = "指定 Cache 最大数量 (默认为 2048)"  // 选项的描述文本
        },

        {
                .identifier = 'h',                    // 选项的唯一标识符
                .access_letters = "h",                // 短选项名为 -h
                .access_name = "help",                // 长选项名为 --help
                .value_name = NULL,                   // 该选项不需要额外的值参数
                .description = "显示本帮助信息，然后退出"  // 选项的描述文本
        }
};

/**
 * @brief DNS中继服务器的配置信息结构体
 *
 * 该结构体用于存储从命令行参数解析得到的所有配置信息，
 * 包括调试级别、网络配置、缓存设置等。
 */
struct Config {
    int debug_level;                    // 调试级别：0-无调试信息，1-基本信息，2-详细信息
    int port;                          // DNS服务器监听端口号（默认53）
    int cache_size;                    // 缓存系统最大容量（默认2048）
    int rto;                           // 请求超时时间(Request Timeout)，单位为毫秒（默认5000ms）
    const char *dns_server_ipaddr;     // 上游DNS服务器的IP地址（默认********）
    const char *filename;              // 黑名单配置文件名（默认dnsrelay.txt）
};

/**
 * @brief 解析命令行参数
 *
 * 该函数使用cargs库解析命令行参数，并将结果存储到Config结构体中。
 * 支持短选项（如-d）和长选项（如--debug）两种格式。
 *
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @param config 用于存储解析结果的配置结构体指针
 * @return 解析成功返回0，失败返回-1
 */
int parse_args(int argc, char **argv, struct Config *config);

/**
 * @brief 打印当前配置信息
 *
 * 该函数用于调试目的，在详细模式下打印所有配置参数的值，
 * 帮助开发者确认参数解析是否正确。
 *
 * @param config 要打印的配置信息结构体指针
 */
void dump_args(struct Config *config);