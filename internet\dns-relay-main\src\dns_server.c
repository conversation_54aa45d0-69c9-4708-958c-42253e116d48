/**
 * @file dns_server.c
 * @brief DNS中继服务器核心实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件实现了DNS中继服务器的核心功能，包括：
 * - 基于libhv的异步UDP服务器
 * - DNS查询请求的接收和处理
 * - 黑名单域名的过滤拦截
 * - DNS查询结果的智能缓存
 * - 上游DNS服务器的查询转发
 *
 * 工作流程：
 * 1. 接收客户端DNS查询请求
 * 2. 检查域名是否在黑名单中
 * 3. 检查缓存中是否有对应记录
 * 4. 如果缓存未命中，向上游DNS服务器查询
 * 5. 将结果缓存并返回给客户端
 */

#include "dns_server.h"

// ============================================================================
// 内部函数声明
// ============================================================================

/**
 * @brief 检查缓存中是否存在查询域名的记录
 * @param server DNS服务器实例指针
 * @param query DNS查询消息
 * @param response DNS响应消息（如果缓存命中则填充）
 * @return 缓存命中返回1，未命中返回0
 */
static int check_cache(dns_server_t* server, dns_t* query, dns_t* response);

/**
 * @brief 构建DNS响应消息
 * @param response 要填充的DNS响应消息
 * @param query 原始DNS查询消息
 * @param addr_cnt 地址数量
 * @param cached_value 缓存的地址数据
 * @param type 记录类型（A或AAAA）
 */
static void build_dns_response(dns_t* response, dns_t* query, int addr_cnt, const char* cached_value, int type);

/**
 * @brief 向上游DNS服务器执行查询
 * @param server DNS服务器实例指针
 * @param query DNS查询消息
 * @param response DNS响应消息（填充查询结果）
 * @return 成功返回0，失败返回-1
 */
static int perform_dns_lookup(dns_server_t* server, dns_t* query, dns_t* response);

/**
 * @brief 从配置文件加载黑名单和IP映射
 * @param blacklist 黑名单缓存实例
 * @param cache IP映射缓存实例
 * @param filename 配置文件路径
 * @return 成功返回0，失败返回-1
 */
static int load_blacklist(cache_t* blacklist, cache_t* cache, const char* filename);

/**
 * @brief 检查域名是否在黑名单中
 * @param blacklist 黑名单缓存实例
 * @param domain 要检查的域名
 * @return 在黑名单中返回true，否则返回false
 */
static bool is_blacklisted(cache_t* blacklist, const char* domain);
// ============================================================================
// DNS服务器生命周期管理函数
// ============================================================================

/**
 * @brief 初始化DNS服务器
 *
 * 该函数执行DNS服务器的完整初始化过程：
 * 1. 创建libhv事件循环
 * 2. 创建UDP服务器并绑定到指定端口
 * 3. 设置网络I/O回调函数
 * 4. 初始化缓存和黑名单系统
 * 5. 加载配置文件中的黑名单和IP映射
 *
 * 网络配置：
 * - 监听所有网络接口（0.0.0.0）
 * - 使用UDP协议（DNS标准）
 * - 端口可配置（默认53）
 *
 * 缓存系统：
 * - DNS查询结果缓存：提高响应速度
 * - 黑名单缓存：快速过滤被拦截域名
 * - 缓存大小可配置（默认2048条）
 *
 * @param server DNS服务器实例指针
 * @param config 服务器配置信息（端口、缓存大小、配置文件等）
 * @return 成功时返回0，失败时返回-1
 */
int dns_server_init(dns_server_t* server, struct Config* config) {
    // 第一步：创建libhv事件循环
    server->loop = hloop_new(HLOOP_FLAG_AUTO_FREE);
    if (server->loop == NULL) {
        hloge("Failed to create event loop");
        return -1;
    }

    // 第二步：创建UDP服务器并绑定到指定端口
    hio_t* io = hloop_create_udp_server(server->loop, "0.0.0.0", config->port);
    if (io == NULL) {
        hloge("Failed to create UDP server on port %d", config->port);
        return -1;
    }

    // 第三步：设置I/O对象的上下文和回调函数
    hio_set_context(io, server);        // 将服务器实例设为I/O上下文
    hio_setcb_read(io, on_recv);        // 设置数据接收回调函数
    hio_read(io);                       // 开始监听和读取数据

    // 第四步：保存配置信息
    server->config = config;

    // 第五步：初始化缓存系统
    server->cache = cache_create(config->cache_size);      // DNS查询结果缓存
    server->blacklist = cache_create(config->cache_size);  // 黑名单缓存

    // 第六步：加载黑名单和IP映射配置文件
    if(load_blacklist(server->blacklist, server->cache, config->filename) != 0) {
        hloge("Failed to load blacklist from file: %s", config->filename);
        return -1;
    }

    hlogi("DNS Server initialized successfully on port %d", config->port);
    hlogi("Cache size: %d, Config file: %s", config->cache_size, config->filename);
    return 0;
}

/**
 * @brief 启动DNS服务器
 *
 * 启动事件循环，开始监听和处理DNS查询请求。
 * 该函数会阻塞运行，直到服务器被停止或发生错误。
 *
 * 事件循环处理：
 * - 监听UDP端口上的DNS查询请求
 * - 处理网络I/O事件
 * - 执行定时器和其他异步任务
 * - 调用注册的回调函数
 *
 * 阻塞特性：
 * - 函数会一直运行直到hloop_stop被调用
 * - 通常在接收到SIGINT信号时停止
 * - 适合在主线程中调用
 *
 * @param server 已初始化的DNS服务器实例指针
 * @return 正常退出时返回0，异常时返回非0值
 */
int dns_server_start(dns_server_t* server) {
    hlogi("DNS Server starting event loop...");
    hlogi("Listening for DNS queries on port %d", server->config->port);
    hlogi("Upstream DNS server: %s", server->config->dns_server_ipaddr);

    // 启动事件循环，开始处理网络事件
    return hloop_run(server->loop);
}

/**
 * @brief 停止DNS服务器
 *
 * 停止事件循环并释放所有相关资源：
 * 1. 停止事件循环，不再处理新的请求
 * 2. 关闭网络监听端口
 * 3. 释放缓存系统内存
 * 4. 清理其他资源
 *
 * 资源清理：
 * - DNS查询结果缓存
 * - 黑名单缓存
 * - 网络连接和套接字
 * - 事件循环相关资源
 *
 * 调用时机：
 * - 通常在信号处理函数中调用
 * - 程序正常退出时调用
 * - 发生不可恢复错误时调用
 *
 * @param server DNS服务器实例指针
 * @return 成功时返回0
 */
int dns_server_stop(dns_server_t* server) {
    hlogi("DNS Server stopping...");

    // 停止事件循环，不再处理新的网络事件
    hloop_stop(server->loop);

    // 释放缓存系统资源
    cache_destroy(server->cache);
    cache_destroy(server->blacklist);

    hlogi("DNS Server stopped successfully");
    return 0;
}

// ============================================================================
// 网络事件回调函数
// ============================================================================

/**
 * @brief UDP数据接收回调函数
 *
 * 当有DNS查询数据到达时，libhv会调用此函数。
 * 该函数负责：
 * 1. 获取客户端地址信息
 * 2. 解包DNS查询消息
 * 3. 调用DNS查询处理函数
 *
 * 错误处理：
 * - 如果DNS消息解包失败，记录错误并返回
 * - 不会向客户端发送错误响应
 *
 * 网络信息：
 * - 使用getpeername获取客户端地址
 * - 支持IPv4和IPv6客户端
 *
 * @param io libhv的I/O对象，包含网络连接信息
 * @param buf 接收到的数据缓冲区
 * @param readbytes 接收到的数据字节数
 */
static void on_recv(hio_t* io, void* buf, int readbytes) {
    // 第一步：获取客户端地址信息
    sockaddr_u client_addr;
    socklen_t addrlen = sizeof(client_addr);
    getpeername(hio_fd(io), (struct sockaddr*)&client_addr, &addrlen);

    // 第二步：解包DNS查询消息
    dns_t query;
    if (dns_unpack((char*)buf, readbytes, &query) < 0) {
        hloge("Failed to unpack DNS query from client");
        return;  // 解包失败，直接返回
    }

    // 第三步：调用DNS查询处理函数
    on_dns_query(io, &query, &client_addr, addrlen);

    // 第四步：释放查询消息的内存
    dns_free(&query);
}

/**
 * @brief DNS查询处理核心函数
 *
 * 该函数实现了DNS中继服务器的核心逻辑：
 * 1. 构造DNS响应消息头部
 * 2. 检查域名是否在黑名单中
 * 3. 检查缓存中是否有对应记录
 * 4. 如果缓存未命中，向上游DNS服务器查询
 * 5. 将结果打包并发送给客户端
 *
 * 处理流程：
 * - 黑名单域名：返回NXDOMAIN响应
 * - 缓存命中：直接返回缓存结果
 * - 缓存未命中：查询上游服务器并缓存结果
 * - 查询失败：返回NXDOMAIN响应
 *
 * 响应类型：
 * - 成功响应：包含A或AAAA记录
 * - 错误响应：RCODE=3（NXDOMAIN）
 *
 * @param io libhv的I/O对象，用于发送响应
 * @param query 解包后的DNS查询消息
 * @param client_addr 客户端地址信息（当前未使用）
 * @param addrlen 客户端地址长度（当前未使用）
 */
static void on_dns_query(hio_t* io, dns_t* query, sockaddr_u* client_addr, socklen_t addrlen) {
    // 第一步：初始化DNS响应消息
    dns_t response;
    memset(&response, 0, sizeof(response));

    // 设置响应头部字段
    response.hdr.transaction_id = query->hdr.transaction_id;  // 复制事务ID
    response.hdr.qr = DNS_RESPONSE;                           // 设置为响应消息
    response.hdr.rd = query->hdr.rd;                          // 复制递归查询标志
    response.hdr.ra = 1;                                      // 设置递归可用标志
    response.hdr.nquestion = query->hdr.nquestion;            // 复制查询记录数量

    // 复制查询记录到响应中
    response.questions = (dns_rr_t*)malloc(sizeof(dns_rr_t) * query->hdr.nquestion);
    memcpy(response.questions, query->questions, sizeof(dns_rr_t) * query->hdr.nquestion);

    // 第二步：获取服务器实例并检查黑名单
    dns_server_t* server = (dns_server_t*)hio_context(io);
    bool blacklisted = is_blacklisted(server->blacklist, query->questions->name);

    // 第三步：处理非黑名单域名的缓存查询
    if (!blacklisted && check_cache(server, query, &response)) {
        // 缓存命中，直接返回缓存结果
        char buf[512];
        int len = dns_pack(&response, buf, sizeof(buf));
        if (len < 0) {
            hloge("Failed to pack DNS response for cached result");
            dns_free(&response);
            return;
        }

        hlogi("Cache hit: %s -> returning cached result", query->questions->name);
        hio_write(io, buf, len);  // 发送响应给客户端
        dns_free(&response);
        return;
    }

    // 第四步：处理缓存未命中的情况
    if (!blacklisted && perform_dns_lookup(server, query, &response) == 0) {
        // 上游查询成功，返回查询结果
        char buf[512];
        int len = dns_pack(&response, buf, sizeof(buf));
        if (len < 0) {
            hloge("Failed to pack DNS response for lookup result");
            dns_free(&response);
            return;
        }

        hlogd("Cache miss: %s -> forwarded to upstream DNS", query->questions->name);
        hio_write(io, buf, len);  // 发送响应给客户端

    } else {
        // 第五步：处理黑名单域名或查询失败的情况
        if(blacklisted) {
            hlogi("Blacklisted domain blocked: %s", query->questions->name);
        } else {
            hloge("Upstream DNS lookup failed for: %s", query->questions->name);
        }

        // 设置NXDOMAIN响应码
        response.hdr.rcode = 3;  // NXDOMAIN

        char buf[512];
        int len = dns_pack(&response, buf, sizeof(buf));
        if (len < 0) {
            hloge("Failed to pack DNS error response");
            dns_free(&response);
            return;
        }

        hio_write(io, buf, len);  // 发送错误响应给客户端
    }

    // 第六步：清理响应消息内存
    dns_free(&response);
}

// ============================================================================
// DNS查询处理辅助函数
// ============================================================================

/**
 * @brief 检查缓存中是否存在查询域名的记录
 *
 * 该函数在缓存中查找指定域名的DNS记录：
 * 1. 检查查询类型是否为A记录（当前只缓存A记录）
 * 2. 在缓存中查找域名对应的IP地址
 * 3. 如果找到，构造DNS响应消息
 *
 * 缓存策略：
 * - 目前只支持A记录（IPv4地址）的缓存
 * - AAAA记录（IPv6地址）暂不缓存
 * - 缓存的是二进制IP地址数据
 *
 * 性能优化：
 * - 使用Trie树实现O(m)时间复杂度查找
 * - LRU算法确保热点数据留在缓存中
 *
 * @param server DNS服务器实例指针
 * @param query DNS查询消息
 * @param response DNS响应消息（如果缓存命中则填充）
 * @return 缓存命中返回1，未命中返回0
 */
static int check_cache(dns_server_t* server, dns_t* query, dns_t* response) {
    // 第一步：检查查询类型（目前只缓存A记录）
    if (query->questions->rtype != DNS_TYPE_A) {
        return 0;  // 非A记录查询，不检查缓存
    }

    // 第二步：在缓存中查找域名
    const char* cached_value = cache_get(server->cache, query->questions->name);
    if (cached_value != NULL) {
        // 第三步：缓存命中，构造DNS响应
        build_dns_response(response, query, 1, cached_value, query->questions->rtype);
        return 1;  // 缓存命中
    }

    return 0;  // 缓存未命中
}

/**
 * @brief 构建DNS响应消息的回答部分
 *
 * 该函数根据查询结果构造DNS响应消息的回答记录：
 * 1. 设置回答记录数量
 * 2. 分配回答记录数组内存
 * 3. 为每个地址创建资源记录
 * 4. 设置记录的各个字段
 *
 * 支持的记录类型：
 * - A记录：IPv4地址，4字节数据
 * - AAAA记录：IPv6地址，16字节数据
 *
 * 记录字段设置：
 * - 域名：复制自查询记录
 * - 类型：A或AAAA
 * - 类别：IN（Internet）
 * - TTL：3600秒（1小时）
 * - 数据：IP地址的二进制表示
 *
 * 内存管理：
 * - 为回答记录数组分配内存
 * - 为每个记录的数据部分分配内存
 * - 调用者需要使用dns_free()释放内存
 *
 * @param response 要填充的DNS响应消息
 * @param query 原始DNS查询消息
 * @param addr_cnt 地址数量
 * @param cached_value 地址数据（二进制格式）
 * @param type 记录类型（DNS_TYPE_A或DNS_TYPE_AAAA）
 */
static void build_dns_response(dns_t* response, dns_t* query, int addr_cnt, const char* cached_value, int type) {
    // 第一步：设置回答记录数量
    response->hdr.nanswer = addr_cnt;

    // 第二步：分配回答记录数组内存
    response->answers = (dns_rr_t*)malloc(sizeof(dns_rr_t) * addr_cnt);

    // 第三步：为每个地址创建资源记录
    for (int i = 0; i < addr_cnt; ++i) {
        dns_rr_t* rr = &response->answers[i];

        // 复制查询记录的基本信息（域名等）
        memcpy(rr, query->questions, sizeof(dns_rr_t));

        // 设置记录的通用字段
        rr->rclass = DNS_CLASS_IN;  // Internet类别
        rr->ttl = 3600;             // TTL设为1小时

        // 根据记录类型设置特定字段
        if (type == DNS_TYPE_A) {
            // IPv4地址记录
            rr->rtype = DNS_TYPE_A;
            rr->datalen = 4;        // IPv4地址长度为4字节
            rr->data = (char*)malloc(4);
            memcpy(rr->data, cached_value + i * 4, 4);  // 复制第i个IPv4地址
        } else if (type == DNS_TYPE_AAAA) {
            // IPv6地址记录
            rr->rtype = DNS_TYPE_AAAA;
            rr->datalen = 16;       // IPv6地址长度为16字节
            rr->data = (char*)malloc(16);
            memcpy(rr->data, cached_value + i * 16, 16);  // 复制第i个IPv6地址
        }
    }
}

/**
 * @brief 向上游DNS服务器执行查询
 *
 * 该函数处理缓存未命中的情况，向上游DNS服务器查询：
 * 1. 根据查询类型选择相应的查询函数
 * 2. 调用nslookup或nslookup6进行实际查询
 * 3. 如果查询成功，构造DNS响应消息
 * 4. 将查询结果缓存（仅A记录）
 *
 * 支持的查询类型：
 * - A记录：使用nslookup查询IPv4地址
 * - AAAA记录：使用nslookup6查询IPv6地址
 *
 * 缓存策略：
 * - A记录：缓存第一个IPv4地址
 * - AAAA记录：暂不缓存（避免缓存复杂性）
 *
 * 地址限制：
 * - 最多处理10个IPv4地址
 * - 最多处理10个IPv6地址
 *
 * @param server DNS服务器实例指针
 * @param query DNS查询消息
 * @param response DNS响应消息（填充查询结果）
 * @return 成功返回0，失败返回-1
 */
static int perform_dns_lookup(dns_server_t* server, dns_t* query, dns_t* response) {
    int addr_cnt = 0;           // 解析到的地址数量
    uint32_t addrs[10];         // IPv4地址数组（最多10个）
    uint8_t addrs6[10][16];     // IPv6地址数组（最多10个）
    int naddr = sizeof(addrs) / sizeof(addrs[0]);      // IPv4地址数组大小
    int naddr6 = sizeof(addrs6) / sizeof(addrs6[0]);   // IPv6地址数组大小

    // 根据查询类型执行相应的DNS查询
    if (query->questions->rtype == DNS_TYPE_A) {
        // IPv4地址查询
        addr_cnt = nslookup(query->questions->name, addrs, naddr, server->config->dns_server_ipaddr);
        if (addr_cnt > 0) {
            // 查询成功，构造DNS响应
            build_dns_response(response, query, addr_cnt, (const char*)addrs, DNS_TYPE_A);

            // 缓存第一个IPv4地址（简化缓存策略）
            cache_insert(server->cache, query->questions->name, (const char*)&addrs[0]);
            hlogi("Cached A record for domain: %s (resolved %d addresses)",
                  query->questions->name, addr_cnt);
        }
    } else if (query->questions->rtype == DNS_TYPE_AAAA) {
        // IPv6地址查询
        addr_cnt = nslookup6(query->questions->name, addrs6, naddr6, server->config->dns_server_ipaddr);
        if (addr_cnt > 0) {
            // 查询成功，构造DNS响应
            build_dns_response(response, query, addr_cnt, (const char*)addrs6, DNS_TYPE_AAAA);

            // 注意：AAAA记录暂不缓存，避免缓存系统复杂化
            hlogi("Resolved AAAA record for domain: %s (%d addresses, not cached)",
                  query->questions->name, addr_cnt);
        }
    }

    // 返回查询结果：成功返回0，失败返回-1
    return addr_cnt > 0 ? 0 : -1;
}

// ============================================================================
// 配置文件处理函数
// ============================================================================

/**
 * @brief 从配置文件加载黑名单和IP映射
 *
 * 该函数解析配置文件，支持两种类型的记录：
 * 1. 黑名单记录：0.0.0.0 domain.com（拦截域名）
 * 2. IP映射记录：*********** local.com（自定义解析）
 *
 * 配置文件格式：
 * - 每行一条记录：IP地址 域名
 * - 0.0.0.0表示黑名单域名
 * - 其他IP地址表示自定义映射
 * - 支持注释行（以#开头）
 * - 空行会被忽略
 *
 * 处理逻辑：
 * - 黑名单域名：存入blacklist缓存，值为空字符串
 * - IP映射：存入cache缓存，值为二进制IP地址
 *
 * 错误处理：
 * - 文件不存在：返回错误
 * - 格式错误的行：跳过处理
 * - IP地址格式错误：跳过处理
 *
 * @param blacklist 黑名单缓存实例
 * @param cache IP映射缓存实例
 * @param filename 配置文件路径
 * @return 成功返回0，失败返回-1
 */
static int load_blacklist(cache_t* blacklist, cache_t* cache, const char* filename) {
    // 第一步：打开配置文件
    FILE* file = fopen(filename, "r");
    if (file == NULL) {
        perror("Failed to open blacklist file");
        return -1;
    }

    char line[512];             // 行缓冲区
    int line_count = 0;         // 行计数器
    int blacklist_count = 0;    // 黑名单条目计数
    int mapping_count = 0;      // IP映射条目计数

    // 第二步：逐行读取和解析配置文件
    while (fgets(line, sizeof(line), file)) {
        line_count++;

        // 去掉行末的换行符
        line[strcspn(line, "\n")] = '\0';

        // 跳过空行和注释行
        if (line[0] == '\0' || line[0] == '#') {
            continue;
        }

        // 第三步：解析IP地址和域名
        char* ip = strtok(line, " \t");      // 使用空格或制表符分割
        char* domain = strtok(NULL, " \t");

        if (ip != NULL && domain != NULL) {
            if (strcmp(ip, "0.0.0.0") == 0) {
                // 黑名单记录：域名被拦截
                cache_insert(blacklist, domain, "");
                blacklist_count++;
                hlogd("Loaded blacklist entry: %s", domain);
            } else {
                // IP映射记录：域名解析到指定IP
                uint32_t addr;
                if (inet_pton(AF_INET, ip, &addr) == 1) {  // 验证IP地址格式
                    cache_insert(cache, domain, (const char*)&addr);
                    mapping_count++;
                    hlogd("Loaded IP mapping: %s -> %s", domain, ip);
                } else {
                    hloge("Invalid IP address format at line %d: %s", line_count, ip);
                }
            }
        } else {
            hloge("Invalid line format at line %d: missing IP or domain", line_count);
        }
    }

    // 第四步：关闭文件并输出统计信息
    fclose(file);
    hlogi("Loaded configuration from %s: %d blacklist entries, %d IP mappings",
          filename, blacklist_count, mapping_count);
    return 0;
}

/**
 * @brief 检查域名是否在黑名单中
 *
 * 该函数在黑名单缓存中查找指定域名：
 * - 如果域名存在于黑名单中，返回true
 * - 如果域名不在黑名单中，返回false
 *
 * 查找性能：
 * - 使用Trie树实现，时间复杂度O(m)，m为域名长度
 * - 支持大小写不敏感匹配
 *
 * @param blacklist 黑名单缓存实例
 * @param domain 要检查的域名
 * @return 在黑名单中返回true，否则返回false
 */
static bool is_blacklisted(cache_t* blacklist, const char* domain) {
    return cache_get(blacklist, domain) != NULL;
}