/**
 * @file cache.c
 * @brief DNS中继服务器缓存系统实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2025
 *
 * 本文件实现了基于Trie树+LRU算法的高效缓存系统。
 *
 * 设计特点：
 * - Trie树：实现O(m)时间复杂度的域名查找（m为域名长度）
 * - LRU算法：最近最少使用淘汰策略，保持缓存热点数据
 * - 双向链表：O(1)时间复杂度的LRU节点移动操作
 * - 内存管理：自动管理键值对的内存分配和释放
 *
 * 支持的字符集：
 * - 数字：0-9 (索引 0-9)
 * - 连字符：- (索引 10)
 * - 点号：. (索引 11)
 * - 字母：A-Z, a-z (索引 12-37，大小写映射到同一索引)
 */

#include "cache.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// ============================================================================
// 常量定义
// ============================================================================

/**
 * @brief Trie树支持的字符集大小
 *
 * 支持的字符：0-9(10个), -(1个), .(1个), A-Z/a-z(26个) = 38个字符
 * 注意：大写和小写字母映射到相同的索引，实现大小写不敏感
 */
#define N 38

// ============================================================================
// 数据结构定义
// ============================================================================

/**
 * @brief LRU双向链表节点结构体
 *
 * 用于维护缓存项的访问顺序，支持O(1)时间复杂度的插入、删除和移动操作。
 */
typedef struct lru_node_t {
    char* key;                      // 键（域名），动态分配内存
    char* value;                    // 值（IP地址等），动态分配内存
    struct lru_node_t* prev;        // 指向前一个节点
    struct lru_node_t* next;        // 指向后一个节点
} lru_node_t;

/**
 * @brief Trie树节点结构体
 *
 * 用于实现快速的域名前缀匹配和查找。
 */
typedef struct trie_node_t {
    struct trie_node_t* children[N]; // 子节点数组，对应38个支持的字符
    lru_node_t* lru_node;           // 指向对应的LRU节点（如果是单词结尾）
    int is_end_of_word;             // 标记是否为完整域名的结尾
} trie_node_t;

/**
 * @brief 缓存系统主结构体
 *
 * 整合Trie树和LRU链表，提供完整的缓存功能。
 */
struct cache_t {
    trie_node_t* root;              // Trie树根节点
    lru_node_t* head;               // LRU链表头节点（最近使用）
    lru_node_t* tail;               // LRU链表尾节点（最久未使用）
    int capacity;                   // 缓存最大容量
    int size;                       // 当前缓存大小
};

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * @brief 将字符转换为Trie树数组索引
 *
 * 字符到索引的映射规则：
 * - '0'-'9' -> 0-9
 * - '-' -> 10
 * - '.' -> 11
 * - 'A'-'Z', 'a'-'z' -> 12-37 (大小写不敏感)
 *
 * @param c 要转换的字符
 * @return 对应的数组索引，无效字符返回-1
 */
int char_to_index(char c) {
    if (c >= '0' && c <= '9') return c - '0';           // 数字字符
    if (c == '-') return 10;                            // 连字符
    if (c == '.') return 11;                            // 点号
    if (c >= 'A' && c <= 'Z') return c - 'A' + 12;     // 大写字母
    if (c >= 'a' && c <= 'z') return c - 'a' + 12;     // 小写字母（映射到与大写相同的索引）
    return -1;                                          // 无效字符
}

/**
 * @brief 创建新的Trie树节点
 *
 * 初始化一个新的Trie树节点，所有子节点指针设为NULL，
 * 单词结尾标志设为false，LRU节点指针设为NULL。
 *
 * @return 新创建的Trie树节点指针
 */
trie_node_t* create_trie_node() {
    trie_node_t* node = (trie_node_t*)malloc(sizeof(trie_node_t));
    node->is_end_of_word = 0;           // 初始化为非单词结尾
    node->lru_node = NULL;              // 初始化LRU节点指针为空

    // 初始化所有子节点指针为NULL
    for (int i = 0; i < N; ++i) {
        node->children[i] = NULL;
    }
    return node;
}

/**
 * @brief 创建新的LRU链表节点
 *
 * 创建一个新的LRU节点，复制键值对字符串，
 * 初始化前后指针为NULL。
 *
 * @param key 键字符串（域名）
 * @param value 值字符串（IP地址等）
 * @return 新创建的LRU节点指针
 */
lru_node_t* create_lru_node(const char* key, const char* value) {
    lru_node_t* node = (lru_node_t*)malloc(sizeof(lru_node_t));
    node->key = strdup(key);            // 复制键字符串
    node->value = strdup(value);        // 复制值字符串
    node->prev = NULL;                  // 初始化前指针
    node->next = NULL;                  // 初始化后指针
    return node;
}

// ============================================================================
// 缓存系统公共接口
// ============================================================================

/**
 * @brief 创建新的缓存实例
 *
 * 分配并初始化缓存结构体，创建Trie树根节点，
 * 初始化LRU链表为空，设置容量和大小。
 *
 * @param capacity 缓存的最大容量
 * @return 新创建的缓存实例指针
 */
cache_t* cache_create(int capacity) {
    cache_t* cache = (cache_t*)malloc(sizeof(cache_t));
    cache->root = create_trie_node();   // 创建Trie树根节点
    cache->head = NULL;                 // LRU链表头指针初始化为空
    cache->tail = NULL;                 // LRU链表尾指针初始化为空
    cache->capacity = capacity;         // 设置缓存容量
    cache->size = 0;                    // 当前大小初始化为0
    return cache;
}

/**
 * @brief 递归释放Trie树节点及其所有子节点
 *
 * 使用深度优先搜索递归释放整个Trie树的内存。
 * 注意：不释放LRU节点的内存，因为它们在LRU链表中单独管理。
 *
 * @param node 要释放的Trie树节点
 */
void free_trie_node(trie_node_t* node) {
    // 递归释放所有子节点
    for (int i = 0; i < N; ++i) {
        if (node->children[i]) {
            free_trie_node(node->children[i]);
        }
    }
    // 释放当前节点（不释放lru_node，因为它在LRU链表中管理）
    free(node);
}

/**
 * @brief 销毁缓存实例并释放所有内存
 *
 * 执行完整的内存清理：
 * 1. 释放LRU链表中的所有节点及其键值对
 * 2. 释放整个Trie树结构
 * 3. 释放缓存结构体本身
 *
 * @param cache 要销毁的缓存实例
 */
void cache_destroy(cache_t* cache) {
    // 释放LRU链表中的所有节点和键值对
    lru_node_t* current = cache->head;
    while (current) {
        lru_node_t* next = current->next;
        free(current->key);         // 释放键字符串
        free(current->value);       // 释放值字符串
        free(current);              // 释放LRU节点
        current = next;
    }

    // 释放Trie树中的所有节点
    free_trie_node(cache->root);

    // 释放cache结构体本身
    free(cache);
}

/**
 * @brief 向缓存中插入或更新键值对
 *
 * 该函数实现了完整的缓存插入逻辑：
 * 1. 在Trie树中查找或创建路径
 * 2. 如果键已存在，更新值并移动到LRU链表头部
 * 3. 如果键不存在，创建新节点并处理容量限制
 * 4. 当缓存满时，淘汰最久未使用的项目
 *
 * @param cache 缓存实例指针
 * @param key 键（域名）
 * @param value 值（IP地址等）
 */
void cache_insert(cache_t* cache, const char* key, const char* value) {
    // ========================================================================
    // 第一步：在Trie树中查找或创建路径
    // ========================================================================
    trie_node_t* node = cache->root;
    int length = strlen(key);

    // 遍历键的每个字符，在Trie树中建立路径
    for (int i = 0; i < length; ++i) {
        int index = char_to_index(key[i]);
        if (index == -1) continue;  // 跳过无效字符

        // 如果子节点不存在，创建新节点
        if (node->children[index] == NULL) {
            node->children[index] = create_trie_node();
        }
        node = node->children[index];
    }
    node->is_end_of_word = 1;  // 标记为完整单词的结尾

    // ========================================================================
    // 第二步：处理已存在的键（更新值并移动到LRU头部）
    // ========================================================================
    if (node->lru_node) {
        lru_node_t* existing_node = node->lru_node;

        // 更新值
        free(existing_node->value);
        existing_node->value = strdup(value);

        // 如果不是头节点，将其移动到链表头部
        if (existing_node != cache->head) {
            // 从当前位置移除
            if (existing_node->prev) existing_node->prev->next = existing_node->next;
            if (existing_node->next) existing_node->next->prev = existing_node->prev;
            if (existing_node == cache->tail) cache->tail = existing_node->prev;

            // 移动到头部
            existing_node->next = cache->head;
            if (cache->head) cache->head->prev = existing_node;
            existing_node->prev = NULL;
            cache->head = existing_node;
        }
        return;  // 更新完成，直接返回
    }

    // ========================================================================
    // 第三步：处理新键的插入
    // ========================================================================

    // 创建新的LRU节点
    lru_node_t* lru_node = create_lru_node(key, value);
    node->lru_node = lru_node;  // 在Trie节点中建立到LRU节点的链接

    // 检查是否需要淘汰最久未使用的项目
    if (cache->size == cache->capacity) {
        // 缓存已满，需要淘汰尾部节点（最久未使用）
        lru_node_t* tail = cache->tail;

        // 更新LRU链表尾指针
        cache->tail = tail->prev;
        if (cache->tail) cache->tail->next = NULL;

        // 在Trie树中找到对应节点并断开链接
        trie_node_t* del_node = cache->root;
        int del_len = strlen(tail->key);
        for (int i = 0; i < del_len; ++i) {
            int index = char_to_index(tail->key[i]);
            if (index == -1) continue;
            del_node = del_node->children[index];
        }
        if (del_node) del_node->lru_node = NULL;  // 断开Trie节点到LRU节点的链接

        // 释放被淘汰节点的内存
        free(tail->key);
        free(tail->value);
        free(tail);
        cache->size--;  // 减少缓存大小
    }

    // 将新节点插入到LRU链表头部
    lru_node->next = cache->head;
    if (cache->head) cache->head->prev = lru_node;
    cache->head = lru_node;

    // 如果这是第一个节点，同时设置为尾节点
    if (cache->tail == NULL) cache->tail = lru_node;

    cache->size++;  // 增加缓存大小
}

/**
 * @brief 从缓存中获取指定键对应的值
 *
 * 该函数实现了完整的缓存查找逻辑：
 * 1. 在Trie树中查找键对应的路径
 * 2. 验证是否为完整的单词且存在对应的值
 * 3. 将访问的节点移动到LRU链表头部（标记为最近使用）
 * 4. 返回对应的值
 *
 * @param cache 缓存实例指针
 * @param key 要查找的键（域名）
 * @return 成功时返回值的指针，键不存在时返回NULL
 */
const char* cache_get(cache_t* cache, const char* key) {
    // ========================================================================
    // 第一步：在Trie树中查找键
    // ========================================================================
    trie_node_t* node = cache->root;
    int length = strlen(key);

    // 遍历键的每个字符，在Trie树中查找路径
    for (int i = 0; i < length; ++i) {
        int index = char_to_index(key[i]);
        if (index == -1) continue;  // 跳过无效字符

        // 如果路径不存在，说明键不在缓存中
        if (node->children[index] == NULL) {
            return NULL;
        }
        node = node->children[index];
    }

    // ========================================================================
    // 第二步：验证是否为有效的缓存项
    // ========================================================================
    if (!node->is_end_of_word || !node->lru_node) {
        return NULL;  // 不是完整单词或没有对应的值
    }

    // ========================================================================
    // 第三步：更新LRU链表（将访问的节点移到头部）
    // ========================================================================
    lru_node_t* lru_node = node->lru_node;

    // 如果已经是头节点，直接返回值
    if (lru_node == cache->head) {
        return lru_node->value;
    }

    // 从当前位置移除节点
    if (lru_node->prev) lru_node->prev->next = lru_node->next;
    if (lru_node->next) lru_node->next->prev = lru_node->prev;
    if (lru_node == cache->tail) cache->tail = lru_node->prev;

    // 将节点移动到链表头部
    lru_node->next = cache->head;
    if (cache->head) cache->head->prev = lru_node;
    cache->head = lru_node;
    lru_node->prev = NULL;

    // 如果链表为空，同时设置为尾节点
    if (cache->tail == NULL) cache->tail = lru_node;

    return lru_node->value;  // 返回对应的值
}
