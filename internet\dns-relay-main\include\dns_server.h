/**
 * @file dns_server.h
 * @brief DNS中继服务器核心头文件 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件定义了DNS中继服务器的核心结构和接口函数，
 * 实现基于libhv的高性能异步DNS服务器。
 *
 * 主要功能：
 * - DNS查询请求的接收和处理
 * - 上游DNS服务器的查询转发
 * - 黑名单域名的过滤拦截
 * - DNS查询结果的智能缓存
 * - 异步I/O事件处理
 */

#pragma once

#include <hv/hexport.h>    // libhv导出定义
#include <hv/hloop.h>      // libhv事件循环
#include <hv/hsocket.h>    // libhv套接字操作
#include <hv/hbuf.h>       // libhv缓冲区管理
#include "dns.h"           // DNS协议处理
#include "args.h"          // 命令行参数处理
#include "logger.h"        // 日志系统
#include "cache.h"         // 缓存系统

/**
 * @brief DNS服务器实例结构体
 *
 * 该结构体包含了DNS服务器运行所需的所有核心组件：
 * - 事件循环：处理网络I/O事件
 * - 配置信息：服务器运行参数
 * - 缓存系统：存储DNS查询结果
 * - 黑名单系统：存储被拦截的域名
 */
typedef struct {
    hloop_t* loop;              // libhv事件循环，处理异步I/O事件
    struct Config* config;      // 服务器配置信息（端口、上游DNS等）
    cache_t* cache;            // DNS查询结果缓存（域名->IP映射）
    cache_t* blacklist;        // 黑名单缓存（存储被拦截的域名）
} dns_server_t;

/**
 * @brief 初始化DNS服务器
 *
 * 该函数执行以下初始化操作：
 * 1. 创建libhv事件循环
 * 2. 创建UDP服务器并绑定到指定端口
 * 3. 初始化缓存和黑名单系统
 * 4. 加载黑名单配置文件
 * 5. 设置网络事件回调函数
 *
 * @param server DNS服务器实例指针
 * @param config 服务器配置信息（端口、缓存大小、配置文件等）
 * @return 成功时返回0，失败时返回-1
 */
int dns_server_init(dns_server_t* server, struct Config* config);

/**
 * @brief 启动DNS服务器
 *
 * 启动事件循环，开始监听DNS查询请求。
 * 该函数会阻塞运行，直到服务器被停止。
 *
 * @param server 已初始化的DNS服务器实例指针
 * @return 正常退出时返回0，异常时返回非0值
 */
int dns_server_start(dns_server_t* server);

/**
 * @brief 停止DNS服务器
 *
 * 停止事件循环，关闭网络监听，释放缓存和黑名单资源。
 * 通常在接收到SIGINT信号时调用。
 *
 * @param server DNS服务器实例指针
 * @return 成功时返回0
 */
int dns_server_stop(dns_server_t* server);

// ============================================================================
// 内部函数声明（仅供dns_server.c内部使用）
// ============================================================================

/**
 * @brief UDP数据接收回调函数
 *
 * 当有DNS查询数据到达时，libhv会调用此函数。
 * 该函数负责解包DNS查询并调用查询处理函数。
 *
 * @param io libhv的I/O对象
 * @param buf 接收到的数据缓冲区
 * @param readbytes 接收到的数据字节数
 */
static void on_recv(hio_t* io, void* buf, int readbytes);

/**
 * @brief DNS查询处理函数
 *
 * 处理解包后的DNS查询请求，执行以下逻辑：
 * 1. 检查域名是否在黑名单中
 * 2. 检查缓存中是否有对应记录
 * 3. 如果缓存未命中，向上游DNS服务器查询
 * 4. 将结果缓存并返回给客户端
 *
 * @param io libhv的I/O对象
 * @param query 解包后的DNS查询消息
 * @param client_addr 客户端地址信息
 * @param addrlen 地址信息长度
 */
static void on_dns_query(hio_t* io, dns_t* query, sockaddr_u* client_addr, socklen_t addrlen);