#!/usr/bin/env python3
"""
dig兼容的DNS查询工具
用于测试DNS服务器，发送标准的DNS查询包
"""

import socket
import struct
import sys
import time
import argparse

def create_dns_query(domain, query_type=1):
    """
    创建标准的DNS查询包
    
    Args:
        domain: 要查询的域名
        query_type: 查询类型 (1=A记录, 28=AAAA记录)
    
    Returns:
        bytes: DNS查询包
    """
    # DNS头部 (12字节)
    transaction_id = 0x1234  # 事务ID
    flags = 0x0100          # 标准查询，递归期望
    questions = 1           # 问题数量
    answer_rrs = 0          # 答案RR数量
    authority_rrs = 0       # 权威RR数量
    additional_rrs = 0      # 附加RR数量
    
    header = struct.pack('!HHHHHH', 
                        transaction_id, flags, questions,
                        answer_rrs, authority_rrs, additional_rrs)
    
    # 编码域名
    domain_parts = domain.split('.')
    encoded_domain = b''
    for part in domain_parts:
        if part:  # 跳过空部分
            encoded_domain += struct.pack('!B', len(part)) + part.encode('ascii')
    encoded_domain += b'\x00'  # 域名结束标志
    
    # 查询部分
    query_class = 1  # IN类
    query_section = encoded_domain + struct.pack('!HH', query_type, query_class)
    
    return header + query_section

def parse_dns_response(data):
    """
    解析DNS响应包
    
    Args:
        data: DNS响应数据
    
    Returns:
        dict: 解析结果
    """
    if len(data) < 12:
        return {"error": "响应包太短"}
    
    # 解析头部
    header = struct.unpack('!HHHHHH', data[:12])
    transaction_id, flags, questions, answers, authority, additional = header
    
    # 检查响应码
    rcode = flags & 0x000F
    response_codes = {
        0: "NOERROR",
        1: "FORMERR", 
        2: "SERVFAIL",
        3: "NXDOMAIN",
        4: "NOTIMP",
        5: "REFUSED"
    }
    
    result = {
        "transaction_id": transaction_id,
        "flags": flags,
        "rcode": rcode,
        "rcode_name": response_codes.get(rcode, f"UNKNOWN({rcode})"),
        "questions": questions,
        "answers": answers,
        "authority": authority,
        "additional": additional,
        "answer_records": []
    }
    
    # 如果有答案，尝试解析
    if answers > 0 and rcode == 0:
        try:
            # 跳过查询部分来找到答案部分
            offset = 12
            # 跳过问题部分
            for _ in range(questions):
                # 跳过域名
                while offset < len(data) and data[offset] != 0:
                    if data[offset] >= 192:  # 压缩指针
                        offset += 2
                        break
                    else:
                        offset += data[offset] + 1
                if offset < len(data) and data[offset] == 0:
                    offset += 1
                offset += 4  # 跳过类型和类
            
            # 解析答案
            for _ in range(answers):
                if offset + 10 > len(data):
                    break
                
                # 跳过名称（可能是压缩指针）
                if data[offset] >= 192:
                    offset += 2
                else:
                    while offset < len(data) and data[offset] != 0:
                        offset += data[offset] + 1
                    offset += 1
                
                if offset + 10 > len(data):
                    break
                
                # 解析RR
                rr_type, rr_class, ttl, rdlength = struct.unpack('!HHIH', data[offset:offset+10])
                offset += 10
                
                if offset + rdlength > len(data):
                    break
                
                rdata = data[offset:offset+rdlength]
                offset += rdlength
                
                # 如果是A记录，解析IP地址
                if rr_type == 1 and rdlength == 4:
                    ip = '.'.join(str(b) for b in rdata)
                    result["answer_records"].append({
                        "type": "A",
                        "ttl": ttl,
                        "data": ip
                    })
        except:
            pass  # 解析失败，忽略
    
    return result

def query_dns(server, port, domain, query_type=1, timeout=5):
    """
    发送DNS查询
    
    Args:
        server: DNS服务器地址
        port: DNS服务器端口
        domain: 要查询的域名
        query_type: 查询类型
        timeout: 超时时间
    
    Returns:
        dict: 查询结果
    """
    try:
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(timeout)
        
        # 创建DNS查询包
        query = create_dns_query(domain, query_type)
        
        # 发送查询
        start_time = time.time()
        sock.sendto(query, (server, port))
        
        # 接收响应
        response, addr = sock.recvfrom(512)
        end_time = time.time()
        
        sock.close()
        
        # 解析响应
        result = parse_dns_response(response)
        result["query_time"] = int((end_time - start_time) * 1000)  # 毫秒
        result["server"] = f"{server}#{port}"
        
        return result
        
    except socket.timeout:
        return {"error": "查询超时"}
    except Exception as e:
        return {"error": f"查询失败: {str(e)}"}

def format_dig_output(domain, result):
    """
    格式化输出，模拟dig的输出格式
    """
    print(f"; <<>> Python dig 1.0 <<>> @{result.get('server', 'unknown')} {domain}")
    print(f"; (1 server found)")
    print(f";; global options: +cmd")
    
    if "error" in result:
        print(f";; {result['error']}")
        print(f";; no servers could be reached")
        return
    
    print(f";; Got answer:")
    print(f";; ->>HEADER<<- opcode: QUERY, status: {result['rcode_name']}, id: {result['transaction_id']}")
    print(f";; flags: qr rd ra; QUERY: {result['questions']}, ANSWER: {result['answers']}, AUTHORITY: {result['authority']}, ADDITIONAL: {result['additional']}")
    print()
    
    print(f";; QUESTION SECTION:")
    print(f";{domain}.\t\t\tIN\tA")
    print()
    
    if result['answers'] > 0 and result['rcode'] == 0:
        print(f";; ANSWER SECTION:")
        for record in result['answer_records']:
            print(f"{domain}.\t\t{record['ttl']}\tIN\t{record['type']}\t{record['data']}")
        print()
    
    print(f";; Query time: {result.get('query_time', 0)} msec")
    print(f";; SERVER: {result.get('server', 'unknown')}")
    print(f";; WHEN: {time.strftime('%a %b %d %H:%M:%S %Z %Y')}")
    print(f";; MSG SIZE  rcvd: {result.get('msg_size', 'unknown')}")

def main():
    parser = argparse.ArgumentParser(description='dig兼容的DNS查询工具')
    parser.add_argument('domain', help='要查询的域名')
    parser.add_argument('-p', '--port', type=int, default=53, help='DNS服务器端口 (默认: 53)')
    parser.add_argument('--server', default='*******', help='DNS服务器地址 (默认: *******)')
    parser.add_argument('-t', '--timeout', type=int, default=5, help='超时时间 (默认: 5秒)')
    
    # 支持 @server 格式
    if len(sys.argv) > 1 and sys.argv[1].startswith('@'):
        server = sys.argv[1][1:]  # 去掉@符号
        sys.argv.pop(1)  # 移除@server参数
        args = parser.parse_args()
        args.server = server
    else:
        args = parser.parse_args()
    
    result = query_dns(args.server, args.port, args.domain, timeout=args.timeout)
    format_dig_output(args.domain, result)

if __name__ == "__main__":
    main()
