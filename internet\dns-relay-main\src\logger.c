/**
 * @file logger.c
 * @brief DNS中继服务器日志系统实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件实现了基于libhv日志系统的日志管理功能，
 * 提供多级别日志输出和彩色显示支持。
 */

#include "logger.h"

/**
 * @brief 初始化日志系统
 *
 * 根据配置信息设置日志系统的各项参数：
 * 1. 设置日志输出处理器为标准输出
 * 2. 根据debug_level设置相应的日志级别
 * 3. 启用彩色日志输出以提高可读性
 * 4. 设置统一的日志格式
 *
 * 日志级别映射：
 * - debug_level 0: LOG_LEVEL_ERROR (仅显示错误信息)
 * - debug_level 1: LOG_LEVEL_INFO (显示信息和错误)
 * - debug_level 2: LOG_LEVEL_DEBUG (显示所有级别的日志)
 *
 * 日志格式说明：
 * %y-%m-%d: 年-月-日
 * %H:%M:%S.%z: 时:分:秒.毫秒
 * %L: 日志级别 (ERROR/INFO/DEBUG)
 * %s: 日志消息内容
 *
 * @param config 包含debug_level的配置信息结构体指针
 */
void init_logger(struct Config *config) {
    // 设置日志处理器为标准输出，使日志直接输出到控制台
    hlog_set_handler(stdout_logger);

    // 根据调试级别设置相应的日志输出级别
    switch (config->debug_level) {
        case 0:
            // 级别0：仅输出错误信息，用于生产环境
            hlog_set_level(LOG_LEVEL_ERROR);
            break;
        case 1:
            // 级别1：输出基本信息，包括查询域名、缓存状态等
            hlog_set_level(LOG_LEVEL_INFO);
            break;
        case 2:
            // 级别2：输出详细调试信息，包括网络通信、内存分配等
            hlog_set_level(LOG_LEVEL_DEBUG);
            break;
        default:
            // 默认使用INFO级别
            hlog_set_level(LOG_LEVEL_INFO);
            break;
    }

    // 启用日志颜色输出，提高日志的可读性
    // 参数0表示自动检测终端是否支持颜色
    logger_enable_color(hlog, 0);

    // 设置日志格式：时间戳 + 日志级别 + 消息内容
    // 示例输出：2024-06-02 10:30:15.123 INFO DNS Server initialized on port 5353
    hlog_set_format("%y-%m-%d %H:%M:%S.%z %L %s");
}