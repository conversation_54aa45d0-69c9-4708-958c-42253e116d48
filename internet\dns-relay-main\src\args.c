#pragma once  // 防止头文件被多次包含（虽然在.c文件中使用不太常见）

#include "args.h"  // 包含自定义的命令行参数处理头文件

#include <stdio.h>  // 包含标准输入输出库，用于printf等函数
#include <stdlib.h>  // 包含标准库，用于atoi等函数

int parse_args(int argc, char **argv, struct Config *config)  // 定义解析命令行参数的函数，返回整数表示成功或失败
{
    config->port = 53;  // 设置默认端口号为53（DNS标准端口）
    config->cache_size = 2048;  // 设置默认缓存大小为2048条记录
    config->rto = 5000;  // 设置默认请求超时时间为5000毫秒（5秒）

    cag_option_context context;  // 声明cargs库的选项上下文结构体变量

    // 准备命令行选项解析器
    cag_option_prepare(&context, options, CAG_ARRAY_SIZE(options), argc, argv);  // 初始化选项上下文，传入选项数组、数组大小和命令行参数
    while (cag_option_fetch(&context)) {  // 循环获取每个命令行选项，直到所有选项都处理完毕
        char identifier = cag_option_get(&context);  // 获取当前选项的标识符
        switch (identifier) {  // 根据选项标识符进行不同处理
            case 'd':  // 如果是-d或--debug选项
                config->debug_level = config->debug_level > 1 ? config->debug_level : 1;  // 设置调试级别为1，除非已经设置了更高级别
                break;
            case 'm':  // 如果是-v或--verbose选项（在args.h中定义为'm'标识符）
                config->debug_level = 2;  // 设置调试级别为2（详细模式）
                break;
            case 's':  // 如果是-s或--server选项
                config->dns_server_ipaddr = cag_option_get_value(&context);  // 获取选项值作为DNS服务器IP地址
                break;
            case 'f':  // 如果是-f或--filename选项
                config->filename = cag_option_get_value(&context);  // 获取选项值作为配置文件名
                break;
            case 't':  // 如果是-t或--timeout选项
                config->rto = atoi(cag_option_get_value(&context));  // 将选项值转换为整数作为请求超时时间
                break;
            case 'p':  // 如果是-p或--port选项
                config->port = atoi(cag_option_get_value(&context));  // 将选项值转换为整数作为端口号
                break;
            case 'c':  // 如果是-c或--cache选项
                config->cache_size = atoi(cag_option_get_value(&context));  // 将选项值转换为整数作为缓存大小
                break;
            case 'h':  // 如果是-h或--help选项
                printf("用法: dns-relay [OPTION]\n"  // 打印帮助信息
                       "OPTION:\n"
                       "  -d, --debug               调试级别 1 (仅输出时间坐标、序号和查询的域名)\n"
                       "  -v, --verbose             调试级别 2 (输出冗长的调试信息)\n"
                       "  -h, --help                显示本帮助信息，然后退出\n"
                       "  -s, --server=VALUE        使用指定的 DNS 服务器 (默认为校园 DNS)\n"
                       "  -t, --timeout=VALUE       指定请求上级 DNS 服务器超时时间 (默认为 5000 ms)\n"
                       "  -p, --port=VALUE          使用指定的端口号 (默认为 53)\n"
                       "  -c, --cache=VALUE         指定 Cache 最大数量 (默认为 2048)\n"
                       "  -f, --filename=FILE       使用指定的配置文件 (默认为 dnsrelay.txt)\n");
                exit(0);  // 显示帮助信息后退出程序，返回状态码0表示正常退出
            default:  // 如果是未知选项
                printf("无法识别的选项: %c\n", identifier);  // 打印错误信息
                break;
        }
    }

    // 如果没有指定 DNS 服务器，则使用默认的 DNS 服务器
    if (config->dns_server_ipaddr == NULL) {  // 检查是否已设置DNS服务器IP地址
//        config->dns_server_ipaddr = "*********";  // 阿里 DNS（被注释掉）
        config->dns_server_ipaddr = "********";     // 校内 DNS（默认使用）
    }

    // 如果没有指定配置文件，则使用默认的配置文件
    if (config->filename == NULL) {  // 检查是否已设置配置文件名
        config->filename = "dnsrelay.txt";  // 设置默认配置文件名
    }

    if (config->debug_level >= 2) {  // 如果调试级别大于等于2（详细模式）
        dump_args(config);  // 打印所有配置参数，用于调试
    }

    return 0;  // 返回0表示解析成功
}

void dump_args(struct Config *config)  // 定义打印配置参数的函数
{
    printf("debug_level: %d\n", config->debug_level);  // 打印调试级别
    printf("dns_server_ipaddr: %s\n", config->dns_server_ipaddr);  // 打印DNS服务器IP地址
    printf("filename: %s\n", config->filename);  // 打印配置文件名
    printf("port: %d\n", config->port);  // 打印端口号
    printf("cache_size: %d\n", config->cache_size);  // 打印缓存大小
    printf("rto: %d\n", config->rto);  // 打印请求超时时间
}