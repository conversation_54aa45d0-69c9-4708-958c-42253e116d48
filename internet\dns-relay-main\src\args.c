/**
 * @file args.c
 * @brief DNS中继服务器命令行参数解析实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件实现了DNS中继服务器的命令行参数解析功能，
 * 使用cargs库提供灵活的参数处理和帮助信息显示。
 *
 * 支持的参数类型：
 * - 布尔型参数：-d, -v, -h（不需要值）
 * - 字符串参数：-s, -f（需要字符串值）
 * - 整数参数：-p, -c, -t（需要数字值）
 */

#include "args.h"    // 包含自定义的命令行参数处理头文件
#include <stdio.h>   // 包含标准输入输出库，用于printf等函数
#include <stdlib.h>  // 包含标准库，用于atoi等函数

/**
 * @brief 解析命令行参数并填充配置结构体
 *
 * 该函数执行以下操作：
 * 1. 设置所有配置项的默认值
 * 2. 使用cargs库逐个解析命令行选项
 * 3. 根据选项类型更新配置值
 * 4. 处理帮助信息显示
 * 5. 验证和补充缺失的配置项
 *
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @param config 用于存储解析结果的配置结构体指针
 * @return 解析成功返回0，失败返回-1
 */
int parse_args(int argc, char **argv, struct Config *config) {
    // ========================================================================
    // 设置默认配置值
    // ========================================================================
    config->port = 53;          // 设置默认端口号为53（DNS标准端口）
    config->cache_size = 2048;  // 设置默认缓存大小为2048条记录
    config->rto = 5000;         // 设置默认请求超时时间为5000毫秒（5秒）
    config->debug_level = 0;    // 设置默认调试级别为0（无调试信息）

    // ========================================================================
    // 初始化cargs选项解析器
    // ========================================================================
    cag_option_context context;  // 声明cargs库的选项上下文结构体变量

    // 准备命令行选项解析器，传入选项定义数组、数组大小和命令行参数
    cag_option_prepare(&context, options, CAG_ARRAY_SIZE(options), argc, argv);

    // ========================================================================
    // 逐个处理命令行选项
    // ========================================================================
    while (cag_option_fetch(&context)) {  // 循环获取每个命令行选项，直到所有选项都处理完毕
        char identifier = cag_option_get(&context);  // 获取当前选项的标识符

        switch (identifier) {  // 根据选项标识符进行不同处理
            case 'd':  // 处理-d或--debug选项
                // 设置调试级别为1，但如果已经设置了更高级别（如-v），则保持不变
                config->debug_level = config->debug_level > 1 ? config->debug_level : 1;
                break;

            case 'm':  // 处理-v或--verbose选项（内部标识符为'm'）
                config->debug_level = 2;  // 设置调试级别为2（详细模式）
                break;

            case 's':  // 处理-s或--server选项
                config->dns_server_ipaddr = cag_option_get_value(&context);  // 获取DNS服务器IP地址
                break;

            case 'f':  // 处理-f或--filename选项
                config->filename = cag_option_get_value(&context);  // 获取配置文件名
                break;

            case 't':  // 处理-t或--timeout选项
                config->rto = atoi(cag_option_get_value(&context));  // 将字符串转换为整数作为超时时间
                break;

            case 'p':  // 处理-p或--port选项
                config->port = atoi(cag_option_get_value(&context));  // 将字符串转换为整数作为端口号
                break;

            case 'c':  // 处理-c或--cache选项
                config->cache_size = atoi(cag_option_get_value(&context));  // 将字符串转换为整数作为缓存大小
                break;
            case 'h':  // 处理-h或--help选项
                // 打印详细的帮助信息，包括所有可用选项和使用示例
                printf("用法: dns-relay [OPTION]\n"
                       "\nDNS中继服务器 - 基于libhv的高性能DNS代理服务器\n"
                       "\n选项说明:\n"
                       "  -d, --debug               调试级别 1 (仅输出时间坐标、序号和查询的域名)\n"
                       "  -v, --verbose             调试级别 2 (输出冗长的调试信息)\n"
                       "  -h, --help                显示本帮助信息，然后退出\n"
                       "  -s, --server=VALUE        使用指定的 DNS 服务器 (默认为校园 DNS ********)\n"
                       "  -t, --timeout=VALUE       指定请求上级 DNS 服务器超时时间 (默认为 5000 ms)\n"
                       "  -p, --port=VALUE          使用指定的端口号 (默认为 53)\n"
                       "  -c, --cache=VALUE         指定 Cache 最大数量 (默认为 2048)\n"
                       "  -f, --filename=FILE       使用指定的配置文件 (默认为 dnsrelay.txt)\n"
                       "\n使用示例:\n"
                       "  dns-relay                 使用默认配置启动服务器\n"
                       "  dns-relay -p 8053 -d     在8053端口启动并开启调试模式\n"
                       "  dns-relay -s 8.8.8.8     使用Google DNS作为上游服务器\n");
                exit(0);  // 显示帮助信息后正常退出程序

            default:  // 处理未知选项
                printf("错误: 无法识别的选项 '%c'\n", identifier);
                printf("使用 -h 或 --help 查看帮助信息\n");
                return -1;  // 返回错误状态
        }
    }

    // ========================================================================
    // 设置默认值（如果用户未指定）
    // ========================================================================

    // 如果没有指定DNS服务器，则使用默认的DNS服务器
    if (config->dns_server_ipaddr == NULL) {
        // config->dns_server_ipaddr = "*********";  // 阿里DNS（备选）
        config->dns_server_ipaddr = "********";      // 校园网DNS（默认）
    }

    // 如果没有指定配置文件，则使用默认的配置文件
    if (config->filename == NULL) {
        config->filename = "dnsrelay.txt";  // 默认黑名单配置文件
    }

    // ========================================================================
    // 调试信息输出
    // ========================================================================

    // 如果启用了详细调试模式，打印所有配置参数
    if (config->debug_level >= 2) {
        dump_args(config);
    }

    return 0;  // 返回0表示解析成功
}

/**
 * @brief 打印当前配置信息（调试用）
 *
 * 该函数用于在详细调试模式下打印所有配置参数的值，
 * 帮助开发者确认参数解析是否正确，以及当前使用的配置。
 *
 * @param config 要打印的配置信息结构体指针
 */
void dump_args(struct Config *config) {
    printf("\n========== DNS中继服务器配置信息 ==========\n");
    printf("调试级别 (debug_level):     %d\n", config->debug_level);
    printf("上游DNS服务器 (server):      %s\n", config->dns_server_ipaddr);
    printf("配置文件 (filename):        %s\n", config->filename);
    printf("监听端口 (port):            %d\n", config->port);
    printf("缓存大小 (cache_size):      %d\n", config->cache_size);
    printf("请求超时 (timeout):         %d ms\n", config->rto);
    printf("==========================================\n\n");
}