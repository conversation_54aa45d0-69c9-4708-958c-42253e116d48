# DNS中继服务器使用文档

## 项目简介

本项目是一个基于C语言开发的高性能DNS中继服务器，主要用于计算机网络课程设计。该服务器实现了DNS查询转发、域名黑名单过滤、智能缓存等功能，使用libhv网络库实现高效的异步I/O处理。

## 主要功能

### 1. DNS查询转发
- 接收客户端DNS查询请求
- 转发请求到上游DNS服务器
- 返回查询结果给客户端
- 支持IPv4 (A记录) 和IPv6 (AAAA记录) 查询

### 2. 域名黑名单过滤
- 支持通过配置文件设置域名黑名单
- 黑名单域名查询直接返回拒绝响应
- 支持IP地址映射，可将特定域名解析到指定IP

### 3. 智能缓存系统
- 使用Trie树 + LRU算法实现高效缓存
- 自动缓存查询结果，提高响应速度
- 支持自定义缓存大小
- 自动淘汰最久未使用的缓存项

### 4. 灵活的配置选项
- 支持自定义监听端口
- 支持指定上游DNS服务器
- 支持多级日志输出
- 支持自定义超时时间

### 5. 高性能异步处理
- 基于libhv事件循环机制
- 支持高并发DNS查询处理
- 内存使用优化

## 系统要求

### 编译环境
- CMake 3.27+
- C11标准编译器 (GCC/Clang/MSVC)
- vcpkg包管理器

### 依赖库
- **libhv**: 高性能跨平台网络库
- **cargs**: 命令行参数解析库

### 运行环境
- Linux/Windows/macOS
- 管理员权限（使用53端口时）

## 安装与编译

### 1. 克隆项目
```bash
git clone <repository-url>
cd dns-relay-main
```

### 2. 安装vcpkg（如果未安装）
```bash
# Linux/macOS
git clone https://github.com/Microsoft/vcpkg.git
./vcpkg/bootstrap-vcpkg.sh

# Windows
git clone https://github.com/Microsoft/vcpkg.git
.\vcpkg\bootstrap-vcpkg.bat
```

### 3. 安装依赖
```bash
# 使用项目中的vcpkg
./vcpkg/vcpkg install
```

### 4. 编译项目
```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake .. -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake

# 编译
cmake --build . --config Release
```

## 配置文件

### dnsrelay.txt 格式
配置文件用于设置域名黑名单和IP映射，格式如下：

```
# 黑名单域名（返回拒绝响应）
0.0.0.0 blocked-domain.com
0.0.0.0 ads.example.com

# IP映射（将域名解析到指定IP）
************* local-server.com
******* custom-dns.com

# 支持的域名示例
************* sina.com
************** sohu.com
************** bupt.edu.cn
```

**配置规则：**
- 每行一条记录，格式为：`IP地址 域名`
- `0.0.0.0` 表示黑名单域名，查询时返回拒绝响应
- 其他IP地址表示将域名解析到该IP
- 支持注释（以#开头的行）
- 空行会被忽略

## 命令行参数

### 基本语法
```bash
./dns_relay [选项]
```

### 可用选项

| 短选项 | 长选项 | 参数 | 默认值 | 说明 |
|--------|--------|------|--------|------|
| -h | --help | 无 | - | 显示帮助信息 |
| -d | --debug | 无 | 关闭 | 调试级别1（基本信息） |
| -v | --verbose | 无 | 关闭 | 调试级别2（详细信息） |
| -p | --port | 端口号 | 53 | 指定监听端口 |
| -s | --server | IP地址 | ******** | 指定上游DNS服务器 |
| -f | --filename | 文件路径 | dnsrelay.txt | 指定配置文件 |
| -c | --cache | 数量 | 2048 | 指定缓存大小 |
| -t | --timeout | 毫秒 | 5000 | 指定请求超时时间 |

## 使用示例

### 1. 基本运行
```bash
# 使用默认配置运行（需要管理员权限）
sudo ./dns_relay

# Windows环境
.\dns_relay.exe
```

### 2. 使用非特权端口
```bash
# 使用5353端口，无需管理员权限
./dns_relay -p 5353
```

### 3. 调试模式
```bash
# 基本调试信息
./dns_relay -p 5353 -d

# 详细调试信息
./dns_relay -p 5353 -v
```

### 4. 自定义配置
```bash
# 指定上游DNS服务器和缓存大小
./dns_relay -p 8053 -s ********* -c 4096

# 使用自定义配置文件
./dns_relay -p 8053 -f /path/to/custom.txt

# 组合多个参数
./dns_relay -p 8053 -s ******* -c 4096 -d -t 3000
```

## 上游DNS服务器配置

### 常用公共DNS服务器

| DNS提供商 | 主DNS | 备DNS | 特点 | 推荐场景 |
|-----------|-------|-------|------|----------|
| Google | ******* | 8.8.4.4 | 全球通用，速度快 | 国外网络环境 |
| Cloudflare | ******* | 1.0.0.1 | 注重隐私，速度快 | 注重隐私保护 |
| 阿里DNS | ********* | 223.6.6.6 | 国内优化 | 国内网络环境 |
| 腾讯DNS | 119.29.29.29 | 182.254.116.116 | 国内优化 | 国内网络环境 |
| 百度DNS | 180.76.76.76 | - | 国内优化 | 国内网络环境 |
| 114DNS | 114.114.114.114 | 114.114.115.115 | 国内老牌 | 国内网络环境 |

### 选择上游DNS的建议

1. **国内用户推荐**：
```bash
# 阿里DNS（推荐）
./dns_relay -p 8053 -s ********* -d

# 腾讯DNS
./dns_relay -p 8053 -s 119.29.29.29 -d
```

2. **国外用户推荐**：
```bash
# Google DNS
./dns_relay -p 8053 -s ******* -d

# Cloudflare DNS
./dns_relay -p 8053 -s ******* -d
```

3. **校园网环境**：
```bash
# 使用校园网DNS（仅在校园网内有效）
./dns_relay -p 8053 -s ******** -d
```

4. **使用当前系统DNS**：
```bash
# 先查看当前DNS：nslookup baidu.com
# 然后使用显示的DNS服务器地址
./dns_relay -p 8053 -s 当前DNS地址 -d
```

## 测试方法

### 1. 使用Python测试脚本（推荐）
```bash
# 运行自动化测试脚本
cd dns-relay-main
python test_dns.py
```

**优点**：
- 自动测试所有功能
- 支持自定义端口
- 详细的结果分析
- 跨平台兼容

### 2. 使用nslookup测试
```bash
# Windows环境（注意：可能存在端口兼容性问题）
nslookup
> server 127.0.0.1
> set port=8053
> baidu.com
> 2qq.cn
> bupt
> exit

# Linux环境
nslookup baidu.com 127.0.0.1 -port=8053
```

### 3. 使用dig测试（推荐）
```bash
# 安装dig工具后使用
dig @127.0.0.1 -p 8053 baidu.com
dig @127.0.0.1 -p 8053 2qq.cn
dig @127.0.0.1 -p 8053 bupt

# 查询特定记录类型
dig @127.0.0.1 -p 8053 google.com A
dig @127.0.0.1 -p 8053 google.com AAAA
```

### 4. 功能验证清单
- [ ] 服务器成功启动，显示监听端口
- [ ] 正常域名解析（如baidu.com）
- [ ] 黑名单域名拦截（如2qq.cn, test0）
- [ ] 缓存功能工作（第二次查询更快）
- [ ] 自定义IP映射（如bupt解析到**************）
- [ ] 日志输出正常

### 5. 性能测试
```bash
# 简单性能测试
time nslookup google.com 127.0.0.1  # 第一次
time nslookup google.com 127.0.0.1  # 第二次（应该更快）

# 使用dnsperf进行性能测试
dnsperf -s 127.0.0.1 -p 8053 -d query.txt
```

## 日志输出说明

### 调试级别1 (-d)
- 显示查询的域名和基本处理信息
- 显示缓存命中/未命中状态
- 显示黑名单拦截信息

### 调试级别2 (-v)
- 包含级别1的所有信息
- 显示详细的网络通信信息
- 显示内存分配和释放信息
- 显示配置参数详情

### 日志示例
```
2024-06-02 10:30:15.123 INFO DNS Server initialized on port 5353
2024-06-02 10:30:20.456 INFO Cache hit: baidu.com
2024-06-02 10:30:25.789 INFO Blacklisted: test0
2024-06-02 10:30:30.012 DEBUG Cache miss: google.com
2024-06-02 10:30:30.345 INFO Cache insert: google.com
```

## 常见问题

### 1. 端口占用问题
**问题**: `bind: Address already in use`
**解决**:
- 使用非特权端口：`./dns_relay -p 8053`
- 或停止占用53端口的服务
- 检查端口占用：`netstat -ano | findstr :端口号`

### 2. 配置文件找不到
**问题**: `fopen: No such file or directory`
**解决**:
- 确保dnsrelay.txt在运行目录下
- 或使用-f参数指定完整路径
- 检查文件权限和路径

### 3. 权限不足
**问题**: 无法绑定53端口
**解决**:
- Linux: 使用sudo运行
- Windows: 以管理员身份运行
- 或使用非特权端口（推荐8053）

### 4. 上游DNS服务器无法连接
**问题**: 正常域名解析失败，日志显示"Not found"
**原因**: 默认上游DNS(********)是校园网内部DNS，外网无法访问
**解决**:
```bash
# 使用Google DNS
./dns_relay -p 8053 -s ******* -d

# 使用阿里DNS（国内推荐）
./dns_relay -p 8053 -s ********* -d

# 使用Cloudflare DNS
./dns_relay -p 8053 -s ******* -d

# 使用当前系统DNS
./dns_relay -p 8053 -s ********** -d
```

### 5. nslookup无法连接自定义端口
**问题**: Windows nslookup设置端口后仍显示"No response from server"
**原因**: Windows nslookup对自定义端口支持有限
**解决**:
- 使用Python测试脚本：`python test_dns.py`
- 安装dig工具：`dig @127.0.0.1 -p 8053 域名`
- 或使用标准53端口（需要管理员权限）

### 6. DNS解析失败
**问题**: 查询返回错误
**解决**:
- 检查上游DNS服务器是否可达
- 使用-s参数指定其他DNS服务器
- 检查网络连接
- 查看服务器日志确认问题

## 性能优化建议

1. **缓存大小**: 根据内存情况调整缓存大小，建议2048-8192
2. **超时时间**: 根据网络环境调整超时时间，建议3000-10000ms
3. **上游DNS**: 选择响应速度快的DNS服务器
4. **系统调优**: 调整系统的文件描述符限制

## 技术架构

### 核心模块
- **main.c**: 程序入口和信号处理
- **dns_server.c**: DNS服务器核心逻辑
- **dns.c**: DNS协议实现
- **cache.c**: Trie树+LRU缓存系统
- **args.c**: 命令行参数解析
- **logger.c**: 日志系统

### 工作流程
1. 初始化服务器和缓存系统
2. 加载黑名单配置文件
3. 启动UDP服务器监听
4. 接收DNS查询请求
5. 检查黑名单和缓存
6. 转发查询到上游DNS
7. 缓存结果并返回响应

## 开发者信息

- **项目类型**: 计算机网络课程设计
- **开发语言**: C11
- **网络库**: libhv
- **构建系统**: CMake + vcpkg
- **许可证**: 开源项目

## 更新日志

- **v1.0**: 基础DNS中继功能
- **v1.1**: 添加缓存系统
- **v1.2**: 添加黑名单过滤
- **v1.3**: 性能优化和错误处理改进
