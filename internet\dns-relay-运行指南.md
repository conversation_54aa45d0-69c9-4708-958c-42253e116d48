# DNS中继服务器运行指南

本指南将详细介绍如何在Windows 11系统上通过WSL（Windows Subsystem for Linux）运行DNS中继服务器项目。

## 环境准备

### 1. WSL安装与配置

如果您尚未安装WSL，请按照以下步骤进行安装：

1. 以管理员身份打开PowerShell，执行以下命令：
   ```powershell
   wsl --install
   ```

2. 重启计算机，WSL将自动完成安装并默认安装Ubuntu发行版。

3. 首次启动WSL时，需要设置用户名和密码。

4. 更新WSL系统：
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

### 2. 安装必要的开发工具

在WSL中安装编译工具和依赖：

```bash
# 安装基本开发工具
sudo apt install -y build-essential cmake git

# 安装vcpkg所需依赖
sudo apt install -y curl zip unzip tar pkg-config
```

## 项目配置与构建

### 1. 安装vcpkg包管理器

vcpkg是一个C/C++库管理器，用于安装项目依赖：

```bash
# 克隆vcpkg仓库
git clone https://github.com/microsoft/vcpkg.git

# 进入vcpkg目录并执行安装脚本
cd vcpkg
./bootstrap-vcpkg.sh

# 将vcpkg添加到环境变量（可选）
export PATH="$PATH:$HOME/vcpkg"
echo 'export PATH="$PATH:$HOME/vcpkg"' >> ~/.bashrc
```

### 2. 克隆项目代码

将项目代码复制到WSL环境中：

```bash
# 假设您已经将项目放在Windows的桌面上
cd /mnt/c/Users/<USER>/Desktop/internet/

# 如果您需要从其他位置复制项目，可以使用cp命令
# 例如：cp -r /mnt/c/path/to/dns-relay-main ~/dns-relay-main
```

### 3. 使用vcpkg安装依赖

```bash
cd dns-relay-main

# 使用vcpkg安装项目依赖
~/vcpkg/vcpkg install
```

这将根据项目中的`vcpkg.json`文件自动安装所需的依赖（libhv和cargs）。

### 4. 配置CMake项目

```bash
# 创建构建目录
mkdir -p build && cd build

# 配置CMake项目，指定vcpkg工具链
cmake .. -DCMAKE_TOOLCHAIN_FILE=~/vcpkg/scripts/buildsystems/vcpkg.cmake
```

### 5. 编译项目

```bash
# 在build目录中编译项目
cmake --build . --config Release
```

## 运行DNS中继服务器

### 1. 基本运行

```bash
# 在build目录中运行编译好的程序
sudo ./dns_relay
```

注意：DNS服务器默认使用53端口，需要管理员权限（sudo）才能绑定该端口。

### 2. 使用命令行参数

```bash
# 显示帮助信息
./dns_relay -h

# 使用调试模式运行
sudo ./dns_relay -d

# 使用详细调试模式运行
sudo ./dns_relay -v

# 指定上游DNS服务器
sudo ./dns_relay -s *******

# 指定监听端口（使用非特权端口不需要sudo）
./dns_relay -p 5353

# 指定黑名单文件
sudo ./dns_relay -f /path/to/dnsrelay.txt

# 指定缓存大小
sudo ./dns_relay -c 4096

# 组合使用多个参数
sudo ./dns_relay -d -s ******* -p 53 -c 4096
```

### 3. 测试DNS服务器

在WSL或Windows中，您可以使用以下命令测试DNS服务器是否正常工作：

```bash
# 在WSL中使用dig命令测试（可能需要安装）
sudo apt install dnsutils
dig @127.0.0.1 -p 53 example.com

# 或使用nslookup命令
nslookup example.com 127.0.0.1
```

在Windows命令提示符或PowerShell中：

```powershell
nslookup example.com 127.0.0.1
```

## 在Windows中直接运行（不使用WSL）

如果您希望在Windows中直接运行而不使用WSL，可以按照以下步骤操作：

### 1. 安装必要工具

- 安装Visual Studio 2019或更高版本，确保包含C++开发组件
- 安装CMake（https://cmake.org/download/）
- 安装Git（https://git-scm.com/download/win）

### 2. 安装vcpkg

```powershell
# 克隆vcpkg仓库
git clone https://github.com/microsoft/vcpkg.git

# 进入vcpkg目录并执行安装脚本
cd vcpkg
.\bootstrap-vcpkg.bat
```

### 3. 构建项目

```powershell
# 进入项目目录
cd C:\Users\<USER>\Desktop\internet\dns-relay-main

# 使用vcpkg安装依赖
C:\path\to\vcpkg\vcpkg install

# 创建构建目录
mkdir build
cd build

# 配置CMake项目
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:\path\to\vcpkg\scripts\buildsystems\vcpkg.cmake

# 构建项目
cmake --build . --config Release
```

### 4. 运行程序

```powershell
# 以管理员身份运行命令提示符或PowerShell，然后执行
cd C:\Users\<USER>\Desktop\internet\dns-relay-main\build\Release
.\dns_relay.exe
```

## 常见问题解决

### 1. 端口被占用

如果53端口已被占用（通常是由系统DNS服务或其他程序占用），您可以：

- 使用`-p`参数指定其他端口
- 在Windows中停止DNS Client服务：
  ```powershell
  # 以管理员身份运行PowerShell
  Stop-Service -Name "Dnscache" -Force
  ```

### 2. 依赖安装失败

如果vcpkg安装依赖失败，可以尝试：

```bash
# 更新vcpkg
cd ~/vcpkg
git pull
./bootstrap-vcpkg.sh

# 手动安装依赖
./vcpkg install libhv cargs
```

### 3. 编译错误

如果遇到编译错误，请确保：

- CMake版本不低于3.27
- 正确指定了vcpkg工具链文件
- 所有依赖都已正确安装

## 结语

按照上述步骤，您应该能够在Windows 11系统的WSL环境中成功运行DNS中继服务器。如果遇到任何问题，请检查日志输出，或参考项目文档获取更多信息。