/**
 * @file dns.c
 * @brief DNS协议处理实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2025
 *
 * 本文件实现了完整的DNS协议处理功能，包括：
 * - DNS消息的打包和解包
 * - 域名编码和解码（DNS格式转换）
 * - DNS查询的发送和接收
 * - 支持A记录和AAAA记录查询
 * - 同步和异步DNS查询接口
 *
 * DNS消息格式：
 * +---------------------+
 * |        Header       |  12字节固定头部
 * +---------------------+
 * |       Question      |  查询部分（可变长度）
 * +---------------------+
 * |        Answer       |  回答部分（可变长度）
 * +---------------------+
 * |      Authority      |  权威部分（可变长度）
 * +---------------------+
 * |      Additional     |  附加部分（可变长度）
 * +---------------------+
 */

#include "dns.h"
#include <hv/hdef.h>
#include <hv/hsocket.h>
#include <hv/herr.h>

// ============================================================================
// 内存管理函数
// ============================================================================

/**
 * @brief 释放DNS消息中分配的所有资源记录
 *
 * 该函数释放DNS消息结构体中动态分配的内存，包括：
 * - 查询记录数组
 * - 回答记录数组
 * - 权威记录数组
 * - 附加记录数组
 *
 * 注意：不释放DNS消息结构体本身，只释放其中的动态分配内存
 *
 * @param dns 需要释放资源的DNS消息指针
 */
void dns_free(dns_t* dns) {
    SAFE_FREE(dns->questions);    // 释放查询记录数组
    SAFE_FREE(dns->answers);      // 释放回答记录数组
    SAFE_FREE(dns->authorities);  // 释放权威记录数组
    SAFE_FREE(dns->addtionals);   // 释放附加记录数组
}

// ============================================================================
// 域名编码解码函数
// ============================================================================

/**
 * @brief 将域名编码为DNS格式
 *
 * DNS域名编码格式说明：
 * 普通格式：www.example.com
 * DNS格式：3www7example3com0
 *
 * 编码规则：
 * 1. 每个标签前面加上长度字节
 * 2. 标签之间不使用点号分隔
 * 3. 最后以0字节结尾
 *
 * 示例转换：
 * "www.example.com" -> [3]www[7]example[3]com[0]
 *
 * @param domain 输入的域名字符串，例如："www.example.com"
 * @param buf 输出缓冲区，存储编码后的DNS格式域名
 * @return 成功时返回编码后的总长度（包括结尾的0字节）
 */
int dns_name_encode(const char* domain, char* buf) {
    const char* p = domain;        // 输入域名的当前读取位置
    char* plen = buf++;           // 指向当前标签长度字节的位置
    int buflen = 1;               // 输出缓冲区的当前长度
    int len = 0;                  // 当前标签的字符数量

    // 遍历输入域名的每个字符
    while (*p != '\0') {
        if (*p != '.') {
            // 普通字符：复制到输出缓冲区并增加标签长度计数
            ++len;
            *buf = *p;
        } else {
            // 遇到点号：结束当前标签，开始新标签
            *plen = len;          // 设置当前标签的长度
            plen = buf;           // 指向下一个标签长度字节的位置
            len = 0;              // 重置标签长度计数
        }
        ++p;                      // 移动到下一个输入字符
        ++buf;                    // 移动到下一个输出位置
        ++buflen;                 // 增加输出长度
    }

    // 处理最后一个标签
    *plen = len;                  // 设置最后一个标签的长度
    *buf = '\0';                  // 添加结尾的0字节
    if (len != 0) {
        ++buflen;                 // 如果有最后一个标签，包括结尾的0字节
    }
    return buflen;
}

/**
 * @brief 将DNS格式的域名解码为普通格式
 *
 * DNS域名解码过程说明：
 * DNS格式：[3]www[7]example[3]com[0]
 * 普通格式：www.example.com
 *
 * 解码规则：
 * 1. 读取第一个长度字节，确定第一个标签长度
 * 2. 逐字符处理，当长度计数器为0时表示当前标签结束
 * 3. 在标签之间添加点号分隔符
 * 4. 遇到0字节时结束解码
 *
 * 注意：此函数不处理DNS压缩指针（0xC0开头的字节）
 *
 * @param buf DNS格式的域名数据，以长度前缀编码
 * @param domain 输出缓冲区，存储解码后的普通域名格式
 * @return 成功时返回从buf中读取的字节数（包括结尾的0字节）
 */
int dns_name_decode(const char* buf, char* domain) {
    const char* p = buf;          // 输入缓冲区的当前读取位置
    int len = *p++;               // 读取第一个标签的长度
    int buflen = 1;               // 从输入缓冲区读取的总字节数

    // 循环处理所有字符，直到遇到结尾的0字节
    while (*p != '\0') {
        if (len-- == 0) {
            // 当前标签处理完毕，开始新标签
            len = *p;             // 读取新标签的长度
            *domain = '.';        // 添加点号分隔符
        } else {
            // 复制当前标签的字符
            *domain = *p;
        }
        ++p;                      // 移动到下一个输入字符
        ++domain;                 // 移动到下一个输出位置
        ++buflen;                 // 增加读取字节计数
    }

    // 添加字符串结尾符
    *domain = '\0';
    ++buflen;                     // 包括结尾的0字节
    return buflen;
}

// ============================================================================
// DNS资源记录打包解包函数
// ============================================================================

/**
 * @brief 打包DNS资源记录为网络传输格式
 *
 * DNS资源记录格式：
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                     NAME                     |  可变长度，DNS编码的域名
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                     TYPE                     |  2字节，记录类型
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                    CLASS                     |  2字节，记录类别
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                     TTL                      |  4字节，生存时间（仅回答记录）
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                   RDLENGTH                   |  2字节，数据长度（仅回答记录）
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 * |                    RDATA                     |  可变长度，记录数据（仅回答记录）
 * +--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
 *
 * @param rr 输入的DNS资源记录结构体指针
 * @param buf 输出缓冲区，存储打包后的二进制数据
 * @param len 输出缓冲区的可用长度
 * @return 成功时返回打包后的总字节数，缓冲区不足时返回-1
 */
int dns_rr_pack(dns_rr_t* rr, char* buf, int len) {
    char* p = buf;                // 输出缓冲区的当前写入位置
    char encoded_name[256];       // 临时缓冲区，存储DNS编码的域名

    // 第一步：编码域名
    int encoded_namelen = dns_name_encode(rr->name, encoded_name);

    // 第二步：计算总的打包长度
    int packetlen = encoded_namelen + 2 + 2 + (rr->data ? (4+2+rr->datalen) : 0);
    //                域名长度    + TYPE + CLASS + (TTL + RDLENGTH + RDATA)

    // 第三步：检查缓冲区是否足够
    if (len < packetlen) {
        return -1;  // 缓冲区不足
    }

    // 第四步：写入域名（DNS编码格式）
    memcpy(p, encoded_name, encoded_namelen);
    p += encoded_namelen;

    // 第五步：写入记录类型（TYPE），转换为网络字节序
    uint16_t* pushort = (uint16_t*)p;
    *pushort = htons(rr->rtype);
    p += 2;

    // 第六步：写入记录类别（CLASS），转换为网络字节序
    pushort = (uint16_t*)p;
    *pushort = htons(rr->rclass);
    p += 2;

    // 第七步：如果有数据部分，写入TTL、数据长度和数据内容
    if (rr->datalen && rr->data) {
        // 写入TTL（生存时间），转换为网络字节序
        uint32_t* puint = (uint32_t*)p;
        *puint = htonl(rr->ttl);
        p += 4;

        // 写入数据长度（RDLENGTH），转换为网络字节序
        pushort = (uint16_t*)p;
        *pushort = htons(rr->datalen);
        p += 2;

        // 写入实际数据（RDATA）
        memcpy(p, rr->data, rr->datalen);
        p += rr->datalen;
    }

    return packetlen;
}

/**
 * @brief 解包DNS资源记录从网络传输格式
 *
 * 该函数从二进制数据中解析DNS资源记录，支持两种类型：
 * 1. 查询记录（Question）：只包含NAME、TYPE、CLASS
 * 2. 回答记录（Answer/Authority/Additional）：包含完整的RR格式
 *
 * DNS压缩指针处理：
 * - 如果域名以0xC0开头，表示使用压缩指针
 * - 压缩指针占用2字节，指向消息中其他位置的域名
 * - 本函数简化处理，跳过压缩指针而不解析实际域名
 *
 * @param buf 输入的二进制数据缓冲区
 * @param len 缓冲区的可用长度
 * @param rr 输出的DNS资源记录结构体指针
 * @param is_question 是否为查询记录（1=查询，0=回答/权威/附加）
 * @return 成功时返回解析的字节数，失败时返回-1
 */
int dns_rr_unpack(char* buf, int len, dns_rr_t* rr, int is_question) {
    char* p = buf;                // 输入缓冲区的当前读取位置
    int off = 0;                  // 已读取的字节偏移量
    int namelen = 0;              // 域名部分的长度

    // 第一步：处理域名部分
    if (*(uint8_t*)p >= 192) {
        // 检测到DNS压缩指针（最高两位为11，即0xC0）
        namelen = 2;              // 压缩指针占用2字节
        // 注意：这里简化处理，不解析压缩指针指向的实际域名
        rr->name[0] = '\0';       // 设置空域名
    } else {
        // 普通域名编码，进行解码
        namelen = dns_name_decode(buf, rr->name);
    }

    // 检查域名解析是否成功
    if (namelen < 0) return -1;
    p += namelen;
    off += namelen;

    // 第二步：检查剩余缓冲区是否足够读取TYPE和CLASS（4字节）
    if (len < off + 4) return -1;

    // 第三步：读取记录类型（TYPE），转换为主机字节序
    uint16_t* pushort = (uint16_t*)p;
    rr->rtype = ntohs(*pushort);
    p += 2;

    // 第四步：读取记录类别（CLASS），转换为主机字节序
    pushort = (uint16_t*)p;
    rr->rclass = ntohs(*pushort);
    p += 2;
    off += 4;

    // 第五步：如果不是查询记录，还需要读取TTL、数据长度和数据内容
    if (!is_question) {
        // 检查剩余缓冲区是否足够读取TTL和RDLENGTH（6字节）
        if (len < off + 6) return -1;

        // 读取TTL（生存时间），转换为主机字节序
        uint32_t* puint = (uint32_t*)p;
        rr->ttl = ntohl(*puint);
        p += 4;

        // 读取数据长度（RDLENGTH），转换为主机字节序
        pushort = (uint16_t*)p;
        rr->datalen = ntohs(*pushort);
        p += 2;
        off += 6;

        // 检查剩余缓冲区是否足够读取数据内容
        if (len < off + rr->datalen) return -1;

        // 设置数据指针（指向缓冲区中的数据，不复制）
        rr->data = p;
        p += rr->datalen;
        off += rr->datalen;
    }

    return off;  // 返回总共解析的字节数
}

// ============================================================================
// DNS消息打包解包函数
// ============================================================================

/**
 * @brief 打包完整的DNS消息为网络传输格式
 *
 * DNS消息结构：
 * +---------------------+
 * |        Header       |  12字节固定头部
 * +---------------------+
 * |       Question      |  查询部分（可变长度）
 * +---------------------+
 * |        Answer       |  回答部分（可变长度）
 * +---------------------+
 * |      Authority      |  权威部分（可变长度）
 * +---------------------+
 * |      Additional     |  附加部分（可变长度）
 * +---------------------+
 *
 * 打包过程：
 * 1. 将DNS头部转换为网络字节序并写入缓冲区
 * 2. 依次打包所有查询记录
 * 3. 依次打包所有回答记录
 * 4. 依次打包所有权威记录
 * 5. 依次打包所有附加记录
 *
 * @param dns 输入的DNS消息结构体指针
 * @param buf 输出缓冲区，存储打包后的二进制数据
 * @param len 输出缓冲区的可用长度
 * @return 成功时返回打包后的总字节数，失败时返回-1
 */
int dns_pack(dns_t* dns, char* buf, int len) {
    // 第一步：检查缓冲区是否足够存储DNS头部
    if (len < sizeof(dnshdr_t)) return -1;

    int off = 0;                  // 当前写入位置的偏移量
    dnshdr_t* hdr = &dns->hdr;    // 指向DNS头部的指针

    // 第二步：将DNS头部转换为网络字节序
    dnshdr_t htonhdr = dns->hdr;  // 复制头部结构体
    htonhdr.transaction_id = htons(hdr->transaction_id);  // 事务ID
    htonhdr.nquestion = htons(hdr->nquestion);            // 查询记录数量
    htonhdr.nanswer = htons(hdr->nanswer);                // 回答记录数量
    htonhdr.nauthority = htons(hdr->nauthority);          // 权威记录数量
    htonhdr.naddtional = htons(hdr->naddtional);          // 附加记录数量

    // 第三步：写入DNS头部
    memcpy(buf, &htonhdr, sizeof(dnshdr_t));
    off += sizeof(dnshdr_t);

    int i;  // 循环计数器

    // 第四步：打包所有查询记录
    for (i = 0; i < hdr->nquestion; ++i) {
        int packetlen = dns_rr_pack(dns->questions+i, buf+off, len-off);
        if (packetlen < 0) return -1;  // 打包失败
        off += packetlen;
    }

    // 第五步：打包所有回答记录
    for (i = 0; i < hdr->nanswer; ++i) {
        int packetlen = dns_rr_pack(dns->answers+i, buf+off, len-off);
        if (packetlen < 0) return -1;  // 打包失败
        off += packetlen;
    }

    // 第六步：打包所有权威记录
    for (i = 0; i < hdr->nauthority; ++i) {
        int packetlen = dns_rr_pack(dns->authorities+i, buf+off, len-off);
        if (packetlen < 0) return -1;  // 打包失败
        off += packetlen;
    }

    // 第七步：打包所有附加记录
    for (i = 0; i < hdr->naddtional; ++i) {
        int packetlen = dns_rr_pack(dns->addtionals+i, buf+off, len-off);
        if (packetlen < 0) return -1;  // 打包失败
        off += packetlen;
    }

    return off;  // 返回总的打包字节数
}

/**
 * @brief 解包完整的DNS消息从网络传输格式
 *
 * 解包过程：
 * 1. 初始化DNS消息结构体为零值
 * 2. 解析DNS头部并转换为主机字节序
 * 3. 根据头部中的计数信息，依次解析各部分记录
 * 4. 为每种类型的记录分配内存数组
 * 5. 逐个解包每条记录
 *
 * 内存管理：
 * - 使用SAFE_ALLOC宏分配内存，确保分配成功
 * - 调用者需要使用dns_free()释放分配的内存
 *
 * @param buf 输入的二进制数据缓冲区
 * @param len 缓冲区的可用长度
 * @param dns 输出的DNS消息结构体指针
 * @return 成功时返回解析的总字节数，失败时返回-1
 */
int dns_unpack(char* buf, int len, dns_t* dns) {
    // 第一步：初始化DNS消息结构体
    memset(dns, 0, sizeof(dns_t));

    // 第二步：检查缓冲区是否足够读取DNS头部
    if (len < sizeof(dnshdr_t)) return -1;

    int off = 0;                  // 当前读取位置的偏移量
    dnshdr_t* hdr = &dns->hdr;    // 指向DNS头部的指针

    // 第三步：读取DNS头部并转换为主机字节序
    memcpy(hdr, buf, sizeof(dnshdr_t));
    off += sizeof(dnshdr_t);
    hdr->transaction_id = ntohs(hdr->transaction_id);  // 事务ID
    hdr->nquestion = ntohs(hdr->nquestion);            // 查询记录数量
    hdr->nanswer = ntohs(hdr->nanswer);                // 回答记录数量
    hdr->nauthority = ntohs(hdr->nauthority);          // 权威记录数量
    hdr->naddtional = ntohs(hdr->naddtional);          // 附加记录数量

    int i;  // 循环计数器

    // 第四步：解包查询记录
    if (hdr->nquestion) {
        int bytes = hdr->nquestion * sizeof(dns_rr_t);
        SAFE_ALLOC(dns->questions, bytes);  // 分配查询记录数组内存
        for (i = 0; i < hdr->nquestion; ++i) {
            int packetlen = dns_rr_unpack(buf+off, len-off, dns->questions+i, 1);
            if (packetlen < 0) return -1;  // 解包失败
            off += packetlen;
        }
    }

    // 第五步：解包回答记录
    if (hdr->nanswer) {
        int bytes = hdr->nanswer * sizeof(dns_rr_t);
        SAFE_ALLOC(dns->answers, bytes);    // 分配回答记录数组内存
        for (i = 0; i < hdr->nanswer; ++i) {
            int packetlen = dns_rr_unpack(buf+off, len-off, dns->answers+i, 0);
            if (packetlen < 0) return -1;  // 解包失败
            off += packetlen;
        }
    }

    // 第六步：解包权威记录
    if (hdr->nauthority) {
        int bytes = hdr->nauthority * sizeof(dns_rr_t);
        SAFE_ALLOC(dns->authorities, bytes); // 分配权威记录数组内存
        for (i = 0; i < hdr->nauthority; ++i) {
            int packetlen = dns_rr_unpack(buf+off, len-off, dns->authorities+i, 0);
            if (packetlen < 0) return -1;  // 解包失败
            off += packetlen;
        }
    }

    // 第七步：解包附加记录
    if (hdr->naddtional) {
        int bytes = hdr->naddtional * sizeof(dns_rr_t);
        SAFE_ALLOC(dns->addtionals, bytes); // 分配附加记录数组内存
        for (i = 0; i < hdr->naddtional; ++i) {
            int packetlen = dns_rr_unpack(buf+off, len-off, dns->addtionals+i, 0);
            if (packetlen < 0) return -1;  // 解包失败
            off += packetlen;
        }
    }

    return off;  // 返回总的解包字节数
}

// ============================================================================
// DNS查询函数（同步和异步）
// ============================================================================

/**
 * @brief 发送DNS查询并同步接收响应
 *
 * 该函数实现完整的DNS查询流程：
 * 1. 将DNS查询消息打包为二进制格式
 * 2. 创建UDP套接字并设置超时
 * 3. 向指定DNS服务器发送查询
 * 4. 接收DNS响应数据
 * 5. 解包响应数据为DNS消息结构
 * 6. 清理资源并返回结果
 *
 * 网络通信：
 * - 使用UDP协议（DNS标准）
 * - 默认端口53
 * - 发送和接收超时均为5秒
 *
 * 错误处理：
 * - 套接字创建失败
 * - 发送数据失败
 * - 接收数据超时
 * - 响应数据解析失败
 *
 * @param query 输入的DNS查询消息结构体指针
 * @param response 输出的DNS响应消息结构体指针
 * @param nameserver DNS服务器IP地址字符串（如"*******"）
 * @return 成功时返回0，失败时返回负数错误码
 */
int dns_query(dns_t* query, dns_t* response, const char* nameserver) {
    char buf[1024];               // 网络传输缓冲区
    int buflen = sizeof(buf);     // 缓冲区大小

    // 第一步：将DNS查询消息打包为二进制格式
    buflen = dns_pack(query, buf, buflen);
    if (buflen < 0) {
        return buflen;            // 打包失败，返回错误码
    }

#ifdef OS_WIN
    // Windows平台可能需要初始化Winsock（已注释）
    // WSAInit();
#endif

    // 第二步：创建UDP套接字
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        perror("socket");
        return ERR_SOCKET;
    }

    // 第三步：设置套接字超时（发送和接收均为5秒）
    so_sndtimeo(sockfd, 5000);   // 发送超时5000毫秒
    so_rcvtimeo(sockfd, 5000);   // 接收超时5000毫秒

    int ret = 0;                  // 返回值
    int nsend, nrecv;            // 发送和接收的字节数
    int nparse;                  // 解析的字节数

    // 第四步：设置DNS服务器地址
    struct sockaddr_in addr;
    socklen_t addrlen = sizeof(addr);
    memset(&addr, 0, addrlen);
    addr.sin_family = AF_INET;                        // IPv4协议族
    addr.sin_addr.s_addr = inet_addr(nameserver);     // DNS服务器IP地址
    addr.sin_port = htons(DNS_PORT);                  // DNS端口（53）

    // 第五步：发送DNS查询数据
    nsend = sendto(sockfd, buf, buflen, 0, (struct sockaddr*)&addr, addrlen);
    if (nsend != buflen) {
        ret = ERR_SENDTO;         // 发送失败
        goto error;
    }

    // 第六步：接收DNS响应数据
    nrecv = recvfrom(sockfd, buf, sizeof(buf), 0, (struct sockaddr*)&addr, &addrlen);
    if (nrecv <= 0) {
        ret = ERR_RECVFROM;       // 接收失败或超时
        goto error;
    }

    // 第七步：解包DNS响应数据
    nparse = dns_unpack(buf, nrecv, response);
    if (nparse != nrecv) {
        ret = -ERR_INVALID_PACKAGE;  // 解包失败，数据格式错误
        goto error;
    }

    // 第八步：清理资源
    error:
    if (sockfd != INVALID_SOCKET) {
        closesocket(sockfd);      // 关闭套接字
    }
    return ret;
}

// ============================================================================
// 高级DNS查询接口
// ============================================================================

/**
 * @brief 进行IPv4域名解析（A记录查询）
 *
 * 该函数是对dns_query的高级封装，专门用于IPv4地址解析：
 * 1. 构造DNS查询消息（A记录类型）
 * 2. 发送查询并接收响应
 * 3. 验证响应的有效性
 * 4. 提取所有A记录的IP地址
 * 5. 返回解析到的地址数量
 *
 * DNS查询参数：
 * - 查询类型：A记录（IPv4地址）
 * - 查询类别：IN（Internet）
 * - 递归查询：启用
 * - 事务ID：使用进程ID
 *
 * 响应验证：
 * - 事务ID匹配
 * - 响应标志正确
 * - 响应码为0（无错误）
 *
 * @param domain 输入的域名字符串（如"www.example.com"）
 * @param addrs 输出的IPv4地址数组，每个地址占用4字节
 * @param naddr 地址数组的最大容量
 * @param nameserver DNS服务器IP地址字符串
 * @return 成功时返回解析到的地址数量，失败时返回负数错误码
 */
int nslookup(const char* domain, uint32_t* addrs, int naddr, const char* nameserver) {
    // 第一步：构造DNS查询消息
    dns_t query;
    memset(&query, 0, sizeof(query));
    query.hdr.transaction_id = getpid();    // 使用进程ID作为事务ID
    query.hdr.qr = DNS_QUERY;               // 设置为查询消息
    query.hdr.rd = 1;                       // 启用递归查询
    query.hdr.nquestion = 1;                // 查询记录数量为1

    // 第二步：构造查询记录（Question）
    dns_rr_t question;
    memset(&question, 0, sizeof(question));
    strncpy(question.name, domain, sizeof(question.name));  // 设置查询域名
    question.rtype = DNS_TYPE_A;            // 查询类型：A记录（IPv4地址）
    question.rclass = DNS_CLASS_IN;         // 查询类别：IN（Internet）

    query.questions = &question;            // 设置查询记录指针

    // 第三步：发送DNS查询并接收响应
    dns_t resp;
    memset(&resp, 0, sizeof(resp));
    int ret = dns_query(&query, &resp, nameserver);
    if (ret != 0) {
        return ret;                         // 查询失败，返回错误码
    }

    // 第四步：验证DNS响应的有效性
    dns_rr_t* rr = resp.answers;           // 指向回答记录数组
    int addr_cnt = 0;                      // 解析到的地址计数

    if (resp.hdr.transaction_id != query.hdr.transaction_id ||  // 事务ID不匹配
        resp.hdr.qr != DNS_RESPONSE ||                          // 不是响应消息
        resp.hdr.rcode != 0) {                                  // 响应码不为0（有错误）
        ret = -ERR_MISMATCH;
        goto end;
    }

    // 第五步：检查是否有回答记录
    if (resp.hdr.nanswer == 0) {
        ret = 0;                           // 没有回答记录，返回0
        goto end;
    }

    // 第六步：提取所有A记录的IP地址
    for (int i = 0; i < resp.hdr.nanswer; ++i, ++rr) {
        if (rr->rtype == DNS_TYPE_A) {     // 只处理A记录
            if (addr_cnt < naddr && rr->datalen == 4) {  // 检查数组容量和数据长度
                memcpy(addrs+addr_cnt, rr->data, 4);     // 复制4字节IPv4地址
            }
            ++addr_cnt;                    // 增加地址计数（即使数组已满也要计数）
        }
    }
    ret = addr_cnt;                        // 返回解析到的地址数量

    // 第七步：清理资源
    end:
    dns_free(&resp);                       // 释放响应消息的内存
    return ret;
}

/**
 * @brief 进行IPv6域名解析（AAAA记录查询）
 *
 * 该函数是nslookup的IPv6版本，专门用于IPv6地址解析：
 * 1. 构造DNS查询消息（AAAA记录类型）
 * 2. 发送查询并接收响应
 * 3. 验证响应的有效性
 * 4. 提取所有AAAA记录的IPv6地址
 * 5. 返回解析到的地址数量
 *
 * DNS查询参数：
 * - 查询类型：AAAA记录（IPv6地址）
 * - 查询类别：IN（Internet）
 * - 递归查询：启用
 * - 事务ID：使用进程ID
 *
 * IPv6地址格式：
 * - 每个地址占用16字节（128位）
 * - 网络字节序存储
 *
 * @param domain 输入的域名字符串（如"www.example.com"）
 * @param addrs 输出的IPv6地址数组，每个地址占用16字节
 * @param naddr 地址数组的最大容量
 * @param nameserver DNS服务器IP地址字符串
 * @return 成功时返回解析到的地址数量，失败时返回负数错误码
 */
int nslookup6(const char* domain, uint8_t addrs[][16], int naddr, const char* nameserver) {
    // 第一步：构造DNS查询消息
    dns_t query;
    memset(&query, 0, sizeof(query));
    query.hdr.transaction_id = getpid();    // 使用进程ID作为事务ID
    query.hdr.qr = DNS_QUERY;               // 设置为查询消息
    query.hdr.rd = 1;                       // 启用递归查询
    query.hdr.nquestion = 1;                // 查询记录数量为1

    // 第二步：构造查询记录（Question）
    dns_rr_t question;
    memset(&question, 0, sizeof(question));
    strncpy(question.name, domain, sizeof(question.name));  // 设置查询域名
    question.rtype = DNS_TYPE_AAAA;         // 查询类型：AAAA记录（IPv6地址）
    question.rclass = DNS_CLASS_IN;         // 查询类别：IN（Internet）

    query.questions = &question;            // 设置查询记录指针

    // 第三步：发送DNS查询并接收响应
    dns_t resp;
    memset(&resp, 0, sizeof(resp));
    int ret = dns_query(&query, &resp, nameserver);
    if (ret != 0) {
        return ret;                         // 查询失败，返回错误码
    }

    // 第四步：验证DNS响应的有效性
    dns_rr_t* rr = resp.answers;           // 指向回答记录数组
    int addr_cnt = 0;                      // 解析到的地址计数

    if (resp.hdr.transaction_id != query.hdr.transaction_id ||  // 事务ID不匹配
        resp.hdr.qr != DNS_RESPONSE ||                          // 不是响应消息
        resp.hdr.rcode != 0) {                                  // 响应码不为0（有错误）
        ret = -ERR_MISMATCH;
        goto end;
    }

    // 第五步：检查是否有回答记录
    if (resp.hdr.nanswer == 0) {
        ret = 0;                           // 没有回答记录，返回0
        goto end;
    }

    // 第六步：提取所有AAAA记录的IPv6地址
    for (int i = 0; i < resp.hdr.nanswer; ++i, ++rr) {
        if (rr->rtype == DNS_TYPE_AAAA) {  // 只处理AAAA记录
            if (addr_cnt < naddr && rr->datalen == 16) {  // 检查数组容量和数据长度
                memcpy(addrs[addr_cnt], rr->data, 16);    // 复制16字节IPv6地址
                ++addr_cnt;                // 增加地址计数
            }
        }
    }
    ret = addr_cnt;                        // 返回解析到的地址数量

    // 第七步：清理资源
    end:
    dns_free(&resp);                       // 释放响应消息的内存
    return ret;
}

// ============================================================================
// 异步DNS查询接口
// ============================================================================

/**
 * @brief 异步发送DNS查询并设置响应回调
 *
 * 该函数实现基于libhv的异步DNS查询：
 * 1. 将DNS查询消息打包为二进制格式
 * 2. 设置响应消息结构体为I/O上下文
 * 3. 配置目标DNS服务器地址
 * 4. 异步发送查询数据
 * 5. 设置读取回调函数
 * 6. 启动异步读取响应
 *
 * 异步特性：
 * - 非阻塞操作，立即返回
 * - 使用事件循环处理I/O
 * - 通过回调函数处理响应
 *
 * 注意事项：
 * - 调用者需要确保query、response和io对象在回调完成前保持有效
 * - 回调函数负责处理响应数据和错误情况
 * - 需要在事件循环中运行
 *
 * @param query 输入的DNS查询消息结构体指针
 * @param response 输出的DNS响应消息结构体指针（在回调中填充）
 * @param nameserver DNS服务器IP地址字符串
 * @param io libhv的I/O对象，用于异步网络操作
 * @param cb 查询完成后的回调函数，参数为(hio_t*, void*, int)
 * @return 成功时返回0，失败时返回负数错误码
 */
int dns_query_async(dns_t* query, dns_t* response, const char* nameserver, hio_t* io,
        void (*cb)(hio_t *, void *, int)) {

    char buf[1024];               // 网络传输缓冲区
    int buflen = sizeof(buf);     // 缓冲区大小

    // 第一步：将DNS查询消息打包为二进制格式
    buflen = dns_pack(query, buf, buflen);
    if (buflen < 0) {
        return buflen;            // 打包失败，返回错误码
    }

    // 第二步：设置I/O上下文为响应结构体，供回调函数使用
    hio_set_context(io, response);

    // 第三步：配置目标DNS服务器地址
    struct sockaddr_in addr;
    socklen_t addrlen = sizeof(addr);
    memset(&addr, 0, addrlen);
    addr.sin_family = AF_INET;                        // IPv4协议族
    addr.sin_addr.s_addr = inet_addr(nameserver);     // DNS服务器IP地址
    addr.sin_port = htons(DNS_PORT);                  // DNS端口（53）

    // 第四步：设置I/O对象的远端地址
    hio_set_peeraddr(io, (struct sockaddr *) &addr, addrlen);

    // 第五步：异步发送DNS查询数据
    hio_write(io, buf, buflen);

    // 第六步：设置读取完成回调函数
    hio_setcb_read(io, cb);

    // 第七步：启动异步读取响应数据
    hio_read(io);

    return 0;  // 成功启动异步操作
}

/**
 * @brief DNS响应处理回调函数示例
 *
 * 该函数是一个示例回调，展示如何处理异步DNS响应：
 * 1. 从I/O上下文中获取响应结构体
 * 2. 解包接收到的二进制数据
 * 3. 处理解包结果或错误
 * 4. 关闭I/O连接
 *
 * 实际使用中，应该根据具体需求实现自定义回调函数。
 *
 * @param io libhv的I/O对象
 * @param buf 接收到的数据缓冲区
 * @param readbytes 接收到的数据字节数
 */
static void on_dns_response(hio_t* io, void* buf, int readbytes) {
    // 第一步：从I/O上下文中获取响应结构体
    dns_t* response = (dns_t*)hio_context(io);

    // 第二步：解包DNS响应数据
    int nparse = dns_unpack((char*)buf, readbytes, response);
    if (nparse < 0) {
        hloge("Failed to unpack DNS response");
        return;
    }

    // 第三步：处理DNS响应（这里只是简单记录日志）
    hlogi("DNS response received and parsed successfully");

    // 第四步：关闭I/O连接
    hio_close(io);
}