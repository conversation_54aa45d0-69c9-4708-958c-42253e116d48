/**
 * @file logger.h
 * @brief DNS中继服务器日志系统头文件 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件定义了基于libhv日志系统的日志管理功能，
 * 支持多级别日志输出和彩色日志显示。
 *
 * 日志级别说明：
 * - 级别0：仅输出错误信息（LOG_LEVEL_ERROR）
 * - 级别1：输出基本信息（LOG_LEVEL_INFO），包括查询域名、缓存状态等
 * - 级别2：输出详细调试信息（LOG_LEVEL_DEBUG），包括网络通信、内存分配等
 */

#pragma once

#include <hv/hlog.h>    // libhv日志系统
#include "args.h"       // 配置信息结构体

/**
 * @brief 初始化日志系统
 *
 * 根据配置信息设置日志系统的各项参数：
 * 1. 设置日志输出处理器为标准输出
 * 2. 根据debug_level设置日志级别
 * 3. 启用彩色日志输出
 * 4. 设置日志格式（包含时间戳、日志级别、消息内容）
 *
 * 日志格式："%y-%m-%d %H:%M:%S.%z %L %s"
 * 示例：2024-06-02 10:30:15.123 INFO DNS Server initialized on port 5353
 *
 * @param config 包含debug_level的配置信息结构体指针
 */
void init_logger(struct Config *config);