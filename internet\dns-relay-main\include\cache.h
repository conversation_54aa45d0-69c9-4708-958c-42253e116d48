/**
 * @file cache.h
 * @brief DNS中继服务器缓存系统头文件 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件定义了基于Trie树+LRU算法的高效缓存系统，
 * 用于存储DNS查询结果和黑名单信息，提高DNS解析性能。
 *
 * 缓存系统特点：
 * - 使用Trie树实现快速域名查找（O(m)时间复杂度，m为域名长度）
 * - 使用LRU算法实现缓存淘汰策略
 * - 支持域名到IP地址的映射存储
 * - 支持黑名单域名的快速检测
 */

#pragma once

#include <stdint.h>

/**
 * @brief 缓存结构体的前向声明
 *
 * 使用不透明指针隐藏内部实现细节，提供更好的封装性。
 * 实际的结构体定义在cache.c文件中。
 */
typedef struct cache_t cache_t;

/**
 * @brief 创建新的缓存实例
 *
 * 创建一个具有指定容量的缓存系统，内部使用Trie树+LRU双向链表实现。
 * 当缓存达到容量上限时，会自动淘汰最久未使用的条目。
 *
 * @param capacity 缓存的最大容量（条目数量）
 * @return 成功时返回缓存实例指针，失败时返回NULL
 */
cache_t* cache_create(int capacity);

/**
 * @brief 销毁缓存实例并释放所有相关内存
 *
 * 该函数会释放Trie树、LRU链表以及所有存储的键值对内存。
 * 调用后cache指针将变为无效，不应再使用。
 *
 * @param cache 要销毁的缓存实例指针
 */
void cache_destroy(cache_t* cache);

/**
 * @brief 向缓存中插入或更新键值对
 *
 * 如果键已存在，则更新其值并将其移动到LRU链表头部。
 * 如果键不存在且缓存未满，则插入新条目。
 * 如果键不存在且缓存已满，则淘汰最久未使用的条目后插入新条目。
 *
 * @param cache 缓存实例指针
 * @param key 键（通常是域名），会被复制存储
 * @param value 值（通常是IP地址的二进制表示），会被复制存储
 */
void cache_insert(cache_t* cache, const char* key, const char* value);

/**
 * @brief 从缓存中获取指定键对应的值
 *
 * 如果键存在，则返回对应的值并将该条目移动到LRU链表头部（标记为最近使用）。
 * 如果键不存在，则返回NULL。
 *
 * @param cache 缓存实例指针
 * @param key 要查找的键（域名）
 * @return 成功时返回值的指针，键不存在时返回NULL
 * @note 返回的指针指向缓存内部存储，调用者不应修改或释放
 */
const char* cache_get(cache_t* cache, const char* key);
