# DNS中继服务器功能介绍

## 项目概述

这是一个基于libhv实现的DNS中继服务器，使用vcpkg作为包管理器。该项目是北京邮电大学计算机学院2024计算机网络课程设计的代码仓库。DNS中继服务器的主要功能是接收DNS查询请求，根据配置进行处理，并返回响应结果。

## 核心功能

1. **DNS请求转发**：将客户端的DNS查询请求转发到上游DNS服务器
2. **本地缓存**：缓存已解析的域名，提高查询效率
3. **黑名单过滤**：根据配置文件过滤特定域名的访问
4. **高效查询**：使用Trie树和LRU缓存算法提高查询效率

## 项目结构

项目由以下主要模块组成：

```
dns-relay-main/
├── include/           # 头文件目录
│   ├── args.h        # 命令行参数处理
│   ├── cache.h       # 缓存管理
│   ├── dns.h         # DNS协议相关定义
│   ├── dns_server.h  # DNS服务器核心功能
│   └── logger.h      # 日志系统
├── src/              # 源代码目录
│   ├── args.c        # 命令行参数处理实现
│   ├── cache.c       # 缓存管理实现
│   ├── dns.c         # DNS协议相关实现
│   ├── dns_server.c  # DNS服务器核心功能实现
│   ├── logger.c      # 日志系统实现
│   └── main.c        # 程序入口
├── CMakeLists.txt    # CMake构建配置
├── dnsrelay.txt      # 域名黑名单配置文件
└── vcpkg.json        # vcpkg依赖配置
```

## 各模块详细介绍

### 1. 主程序 (main.c)

主程序是整个DNS中继服务器的入口点，主要完成以下工作：
- 解析命令行参数
- 初始化日志系统
- 初始化DNS服务器
- 启动DNS服务器
- 注册信号处理函数，确保程序能够优雅退出

### 2. 命令行参数处理 (args.h/args.c)

该模块负责处理用户输入的命令行参数，支持的参数包括：
- `-d/--debug`：调试级别1，输出基本信息
- `-v/--verbose`：调试级别2，输出详细调试信息
- `-s/--server`：指定上游DNS服务器地址
- `-f/--filename`：指定黑名单配置文件
- `-p/--port`：指定服务器监听端口
- `-t/--timeout`：指定请求超时时间
- `-c/--cache`：指定缓存大小
- `-h/--help`：显示帮助信息

### 3. DNS协议实现 (dns.h/dns.c)

该模块实现了DNS协议的核心功能，包括：
- DNS消息的打包和解包
- DNS域名的编码和解码
- DNS资源记录的处理
- 网络字节序的转换

模块定义了DNS报文的结构体和各种常量，如DNS查询类型、记录类型等，并提供了一系列函数用于DNS消息的处理。

### 4. DNS服务器 (dns_server.h/dns_server.c)

DNS服务器是整个项目的核心模块，负责：
- 初始化UDP服务器
- 接收和处理DNS查询请求
- 查询本地缓存
- 转发请求到上游DNS服务器
- 处理黑名单域名
- 构建DNS响应消息

服务器使用libhv的事件循环机制处理请求，实现了高效的异步I/O。

### 5. 缓存系统 (cache.h/cache.c)

缓存系统使用Trie树和LRU（最近最少使用）算法实现，主要功能包括：
- 创建和销毁缓存
- 插入缓存项
- 查询缓存项
- 自动淘汰最久未使用的缓存项

Trie树用于快速查找域名，LRU算法用于管理缓存的生命周期，确保缓存大小不会无限增长。

### 6. 日志系统 (logger.h/logger.c)

日志系统基于libhv的日志功能，提供了不同级别的日志输出：
- ERROR：错误信息
- INFO：一般信息
- DEBUG：调试信息

用户可以通过命令行参数设置日志级别，控制输出的详细程度。

## 工作流程

1. **初始化阶段**：
   - 解析命令行参数
   - 初始化日志系统
   - 创建缓存和黑名单
   - 加载黑名单配置文件
   - 初始化UDP服务器

2. **运行阶段**：
   - 接收客户端DNS查询请求
   - 解析DNS查询消息
   - 检查域名是否在黑名单中
   - 检查缓存中是否有对应记录
   - 如果缓存命中，直接返回结果
   - 如果缓存未命中，转发请求到上游DNS服务器
   - 接收上游DNS服务器的响应
   - 将结果加入缓存
   - 返回响应给客户端

3. **清理阶段**：
   - 释放缓存资源
   - 释放黑名单资源
   - 关闭UDP服务器

## 特色功能

1. **高效的缓存系统**：使用Trie树和LRU算法实现高效的域名查询和缓存管理
2. **灵活的配置选项**：支持多种命令行参数，可以灵活配置服务器行为
3. **黑名单过滤**：可以通过配置文件指定需要过滤的域名
4. **详细的日志输出**：提供多级日志输出，方便调试和监控
5. **异步I/O**：使用libhv的事件循环机制，实现高效的异步I/O处理

## 依赖库

- **libhv**：高性能的跨平台网络库，提供事件循环、套接字、日志等功能
- **cargs**：轻量级的命令行参数解析库

这些依赖通过vcpkg包管理器进行管理，确保项目能够在不同环境下一致构建。