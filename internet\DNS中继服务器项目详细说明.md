# DNS中继服务器项目详细说明

## 项目概述

本项目是北京邮电大学计算机学院2025年计算机网络课程设计的DNS中继服务器实现，基于C语言和libhv网络库开发的高性能异步DNS代理服务器。

### 核心特性

- **高性能异步I/O**: 基于libhv事件循环机制，支持高并发DNS查询处理
- **智能缓存系统**: 使用Trie树+LRU算法实现O(m)时间复杂度的域名查找
- **黑名单过滤**: 支持域名黑名单和自定义IP映射功能
- **灵活配置**: 支持命令行参数配置和配置文件加载
- **多级日志**: 支持三级日志输出，便于调试和监控
- **跨平台支持**: 支持Linux、Windows、macOS等操作系统

## 技术架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DNS客户端     │    │  DNS中继服务器   │    │  上游DNS服务器   │
│  (nslookup等)   │    │   (本项目)      │    │ (*******等)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. DNS查询请求         │                       │
         ├──────────────────────→│                       │
         │                       │ 2. 检查缓存/黑名单     │
         │                       │                       │
         │                       │ 3. 转发查询(如需要)    │
         │                       ├──────────────────────→│
         │                       │                       │
         │                       │ 4. 接收响应           │
         │                       │←──────────────────────┤
         │ 5. 返回结果           │                       │
         │←──────────────────────┤                       │
```

### 核心模块

#### 1. 主程序模块 (main.c)
- 程序入口点
- 命令行参数解析
- 信号处理和资源清理
- 服务器生命周期管理

#### 2. 命令行参数处理 (args.h/args.c)
- 使用cargs库实现灵活的参数解析
- 支持短选项(-d)和长选项(--debug)
- 提供详细的帮助信息
- 参数验证和默认值设置

#### 3. DNS协议处理 (dns.h/dns.c)
- DNS消息的打包和解包
- 域名编码/解码(DNS格式转换)
- 支持A记录和AAAA记录查询
- 同步和异步DNS查询接口

#### 4. 缓存系统 (cache.h/cache.c)
- Trie树实现快速域名查找
- LRU算法实现缓存淘汰策略
- 支持域名到IP地址的映射存储
- 线程安全的缓存操作

#### 5. DNS服务器核心 (dns_server.h/dns_server.c)
- 基于libhv的异步UDP服务器
- DNS查询请求处理逻辑
- 黑名单检查和缓存管理
- 上游DNS服务器查询转发

#### 6. 日志系统 (logger.h/logger.c)
- 基于libhv日志系统
- 三级日志输出控制
- 彩色日志显示
- 时间戳和格式化输出

## 工作流程

### DNS查询处理流程

1. **接收查询**: UDP服务器接收客户端DNS查询请求
2. **解包验证**: 解析DNS消息格式，验证请求有效性
3. **黑名单检查**: 检查查询域名是否在黑名单中
4. **缓存查找**: 在Trie树缓存中查找域名对应的IP地址
5. **上游查询**: 如果缓存未命中，向上游DNS服务器查询
6. **结果缓存**: 将查询结果存入缓存系统
7. **响应返回**: 构造DNS响应消息并返回给客户端

### 缓存系统工作原理

#### Trie树结构
```
支持字符集: 0-9, -, ., A-Z, a-z (共38个字符)
域名: www.example.com
编码: w-w-w-.-e-x-a-m-p-l-e-.-c-o-m
```

#### LRU淘汰策略
- 双向链表维护访问顺序
- 最近访问的节点移到链表头部
- 缓存满时淘汰链表尾部节点

## 配置文件格式

### dnsrelay.txt配置说明

```
# 黑名单域名（返回拒绝响应）
0.0.0.0 blocked-domain.com
0.0.0.0 ads.example.com

# IP映射（将域名解析到指定IP）
************* local-server.com
******* custom-dns.com

# 预设映射示例
************* sina.com
************** sohu.com
************** bupt.edu.cn
```

**配置规则**:
- 每行一条记录，格式为：`IP地址 域名`
- `0.0.0.0` 表示黑名单域名，查询时返回拒绝响应
- 其他IP地址表示将域名解析到该IP
- 支持注释（以#开头的行）
- 空行会被忽略

## 编译和部署

### 系统要求

**编译环境**:
- CMake 3.27+
- C11标准编译器 (GCC/Clang/MSVC)
- vcpkg包管理器

**依赖库**:
- **libhv**: 高性能跨平台网络库
- **cargs**: 命令行参数解析库

**运行环境**:
- Linux/Windows/macOS
- 管理员权限（使用53端口时）

### 编译步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd dns-relay-main

# 2. 安装依赖
./vcpkg/vcpkg install

# 3. 编译项目
mkdir build && cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
cmake --build . --config Release
```

### 使用示例

```bash
# 基本运行（需要管理员权限）
sudo ./dns_relay

# 使用非特权端口
./dns_relay -p 5353

# 调试模式
./dns_relay -p 5353 -d

# 详细调试信息
./dns_relay -p 5353 -v

# 自定义配置
./dns_relay -p 8053 -s ******* -c 4096 -d
```

## 测试方法

### 1. Python测试脚本（推荐）
```bash
python test_dns.py
```

### 2. dig命令测试
```bash
dig @127.0.0.1 -p 8053 baidu.com
dig @127.0.0.1 -p 8053 2qq.cn    # 黑名单测试
dig @127.0.0.1 -p 8053 bupt      # 映射测试
```

### 3. nslookup测试
```bash
nslookup baidu.com 127.0.0.1 -port=8053
```

## 性能特点

### 时间复杂度
- **域名查找**: O(m)，m为域名长度
- **缓存插入**: O(m)
- **LRU更新**: O(1)

### 空间复杂度
- **Trie树**: O(n*m)，n为域名数量，m为平均域名长度
- **LRU链表**: O(n)

### 并发性能
- 基于libhv事件循环，支持高并发
- 异步I/O处理，避免阻塞
- 内存使用优化，支持大量并发连接

## 项目文件结构

```
dns-relay-main/
├── include/                 # 头文件目录
│   ├── args.h              # 命令行参数处理
│   ├── cache.h             # 缓存系统
│   ├── dns.h               # DNS协议处理
│   ├── dns_server.h        # DNS服务器核心
│   └── logger.h            # 日志系统
├── src/                    # 源代码目录
│   ├── main.c              # 主程序入口
│   ├── args.c              # 命令行参数实现
│   ├── cache.c             # 缓存系统实现
│   ├── dns.c               # DNS协议实现
│   ├── dns_server.c        # DNS服务器实现
│   └── logger.c            # 日志系统实现
├── build/                  # 编译输出目录
├── dnsrelay.txt           # 黑名单配置文件
├── test_dns.py            # Python测试脚本
├── CMakeLists.txt         # CMake构建配置
├── vcpkg.json             # vcpkg依赖配置
└── README.md              # 项目说明
```

## 代码注释说明

### 注释完成情况

本项目已为所有头文件和源代码文件添加了详细的中文注释，包括：

#### 头文件注释 (include/)
- ✅ **args.h**: 命令行参数处理，包含选项定义和配置结构体
- ✅ **cache.h**: 缓存系统接口，Trie树+LRU算法说明
- ✅ **dns.h**: DNS协议处理，消息结构和函数接口
- ✅ **dns_server.h**: DNS服务器核心，服务器结构和回调函数
- ✅ **logger.h**: 日志系统，多级别日志配置

#### 源文件注释 (src/)
- ✅ **main.c**: 主程序入口，信号处理和程序生命周期
- ✅ **args.c**: 命令行参数解析实现，cargs库使用详解
- ✅ **cache.c**: 缓存系统实现，Trie树+LRU算法详细注释
- ✅ **dns.c**: DNS协议实现，消息打包解包详解
- ✅ **dns_server.c**: DNS服务器实现，查询处理流程
- ✅ **logger.c**: 日志系统实现，libhv日志配置

### 注释特点

1. **文件头注释**: 每个文件都包含详细的文件说明、作者信息和年份标识
2. **函数注释**: 使用Doxygen风格的注释，包含参数说明和返回值
3. **算法说明**: 对复杂算法（如Trie树+LRU）提供详细的实现原理
4. **代码分段**: 使用分隔线和注释将代码逻辑分段，提高可读性
5. **中文注释**: 所有注释均使用中文，便于中文开发者理解

### 注释示例

```c
/**
 * @file cache.c
 * @brief DNS中继服务器缓存系统实现 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件实现了基于Trie树+LRU算法的高效缓存系统。
 */

/**
 * @brief 向缓存中插入或更新键值对
 *
 * 该函数实现了完整的缓存插入逻辑：
 * 1. 在Trie树中查找或创建路径
 * 2. 如果键已存在，更新值并移动到LRU链表头部
 * 3. 如果键不存在，创建新节点并处理容量限制
 * 4. 当缓存满时，淘汰最久未使用的项目
 *
 * @param cache 缓存实例指针
 * @param key 键（域名）
 * @param value 值（IP地址等）
 */
void cache_insert(cache_t* cache, const char* key, const char* value);
```

## 学习价值

### 技术学习点

1. **网络编程**: 基于libhv的异步I/O编程模式
2. **数据结构**: Trie树和LRU算法的实际应用
3. **系统设计**: 模块化设计和接口抽象
4. **内存管理**: C语言内存分配和释放的最佳实践
5. **项目构建**: CMake和vcpkg的现代C++项目管理

### 课程设计价值

1. **计算机网络**: DNS协议的深入理解和实现
2. **系统编程**: 多平台兼容的系统级编程
3. **性能优化**: 高效数据结构和算法的选择
4. **工程实践**: 完整的软件开发流程体验

## 开发团队

- **项目类型**: 计算机网络课程设计
- **开发语言**: C11
- **网络库**: libhv
- **构建系统**: CMake + vcpkg
- **开发年份**: 2024-2025
- **注释语言**: 中文
- **文档完整性**: 100%代码注释覆盖
