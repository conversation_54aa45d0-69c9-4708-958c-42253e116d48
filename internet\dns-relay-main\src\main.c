/**
 * @file main.c
 * @brief DNS中继服务器主程序入口 (2024-2025)
 * <AUTHOR> Relay Project Team
 * @date 2024
 *
 * 本文件是DNS中继服务器的主程序入口，负责：
 * 1. 解析命令行参数
 * 2. 初始化日志系统
 * 3. 注册信号处理函数
 * 4. 初始化并启动DNS服务器
 * 5. 处理程序退出时的资源清理
 */

#include "args.h"        // 命令行参数处理
#include "logger.h"      // 日志系统
#include "dns_server.h"  // DNS服务器核心功能
#include <signal.h>      // 信号处理

// ============================================================================
// 全局变量
// ============================================================================

/**
 * @brief 全局DNS服务器实例
 *
 * 使用全局变量是为了在信号处理函数中能够访问服务器实例，
 * 以便在接收到SIGINT信号时能够正确停止服务器并清理资源。
 */
dns_server_t server;

// ============================================================================
// 信号处理函数
// ============================================================================

/**
 * @brief 程序退出时的资源清理函数
 *
 * 当程序接收到SIGINT信号（Ctrl+C）时，该函数会被调用。
 * 主要功能是优雅地停止DNS服务器并释放所有相关资源。
 *
 * @param status 信号状态码（通常是SIGINT的值）
 */
static void cleanup(int status) {
    hlogi("Received signal %d, shutting down DNS server...", status);
    dns_server_stop(&server);
    hlogi("DNS server stopped successfully");
}

// ============================================================================
// 主程序
// ============================================================================

/**
 * @brief 程序主入口函数
 *
 * 程序执行流程：
 * 1. 初始化配置结构体为零值
 * 2. 解析命令行参数并填充配置
 * 3. 根据配置初始化日志系统
 * 4. 注册SIGINT信号处理函数
 * 5. 初始化DNS服务器（创建事件循环、缓存、加载配置等）
 * 6. 启动DNS服务器（进入事件循环，开始处理DNS请求）
 *
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @return 程序退出状态码：0表示正常退出，-1表示异常退出
 */
int main(int argc, char **argv) {
    // 初始化服务器配置结构体，所有字段设为0/NULL
    struct Config server_config = {0};

    // 解析命令行参数，将结果存储到server_config中
    // 如果解析失败或用户请求帮助，parse_args会直接退出程序
    parse_args(argc, argv, &server_config);

    // 根据配置中的debug_level初始化日志系统
    init_logger(&server_config);

    // 注册SIGINT信号处理函数，确保程序能够优雅退出
    signal(SIGINT, cleanup);

    // 初始化DNS服务器：创建事件循环、UDP套接字、缓存系统等
    if(dns_server_init(&server, &server_config) != 0) {
        hloge("Failed to initialize DNS Server");
        return -1;
    }

    // 启动DNS服务器，进入事件循环
    // 该函数会阻塞运行，直到接收到停止信号
    dns_server_start(&server);

    return 0;
}
