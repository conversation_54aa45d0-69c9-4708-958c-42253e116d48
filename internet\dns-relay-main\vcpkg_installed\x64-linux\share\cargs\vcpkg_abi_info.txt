cmake 3.30.1
features core
portfile.cmake 18d3be5556539fe281366f3cde6ed41476fbd2f9b7851d80d6fdabe51174f924
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1211256efcbee06e9d815c047677831b91cd50b0
vcpkg-cmake 5d63033154ac260e035cc8e4ca023523cb793080536245442ffc3a0761ebb458
vcpkg-cmake-config df38c9b19cc47b36ee4a13a2ea0e1221d91f48e14a53abd74fc85a1d932e8769
vcpkg.json 7b8a260773cab12f45ee8a1851b27bdef6674e5c26b113565039a4dfe23db178
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
