#!/bin/bash

# DNS中继服务器nslookup测试脚本
# 用于测试运行在端口5353的DNS服务器

echo "=== DNS中继服务器 nslookup 测试 ==="
echo ""

SERVER="127.0.0.1"
PORT="5353"

# 测试函数
test_domain_nslookup() {
    local domain=$1
    local description=$2
    
    echo "--- 测试 $description ---"
    echo "查询域名: $domain"
    echo "DNS服务器: $SERVER:$PORT"
    
    # 创建临时的nslookup命令文件
    cat > /tmp/nslookup_commands << EOF
server $SERVER
set port=$PORT
$domain
exit
EOF
    
    # 执行nslookup
    result=$(timeout 10 nslookup < /tmp/nslookup_commands 2>&1)
    
    if echo "$result" | grep -q "Non-authoritative answer"; then
        echo "✓ 查询成功"
        echo "$result" | grep -A 5 "Non-authoritative answer" | head -3
    elif echo "$result" | grep -q "server can't find.*NXDOMAIN"; then
        echo "✓ 域名不存在 (NXDOMAIN) - 黑名单拦截成功"
    elif echo "$result" | grep -q "connection refused\|timed out\|no servers could be reached"; then
        echo "✗ 连接失败"
        echo "可能的原因："
        echo "  1. DNS服务器未在端口$PORT上运行"
        echo "  2. nslookup不支持自定义端口"
    else
        echo "? 未知响应"
        echo "$result" | head -3
    fi
    
    # 清理临时文件
    rm -f /tmp/nslookup_commands
    
    echo "----------------------------------------"
    echo ""
}

# 检查nslookup是否安装
if ! command -v nslookup &> /dev/null; then
    echo "错误: nslookup命令未找到"
    echo "请安装nslookup: sudo apt-get install dnsutils"
    exit 1
fi

# 检查DNS服务器是否在运行
echo "检查DNS服务器状态..."
if netstat -ln 2>/dev/null | grep -q ":$PORT "; then
    echo "✓ 检测到端口$PORT上有服务在运行"
else
    echo "⚠ 警告: 未检测到端口$PORT上的服务"
    echo "请确保DNS服务器正在运行: ./dns_relay -p $PORT -s 8.8.8.8 -d"
fi
echo ""

# 执行测试
test_domain_nslookup "baidu.com" "正常域名"
test_domain_nslookup "2qq.cn" "黑名单域名"
test_domain_nslookup "test0" "黑名单域名"
test_domain_nslookup "bupt" "映射域名"
test_domain_nslookup "google.com" "正常域名"

echo "=== 测试完成 ==="
echo ""
echo "说明："
echo "- ✓ 表示测试通过"
echo "- ✗ 表示测试失败"
echo "- ? 表示结果不确定"
echo ""
echo "注意: nslookup在某些环境下对自定义端口的支持有限。"
echo "如果nslookup测试失败，建议使用Python测试脚本: python3 test_dns.py"
