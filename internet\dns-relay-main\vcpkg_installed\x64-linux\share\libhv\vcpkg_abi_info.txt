cmake 3.30.1
features core
portfile.cmake c3e20af3a059b89d6030472c55d48148f7e9b829da55c778615bc17451b1d0f6
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1211256efcbee06e9d815c047677831b91cd50b0
vcpkg-cmake 5d63033154ac260e035cc8e4ca023523cb793080536245442ffc3a0761ebb458
vcpkg-cmake-config df38c9b19cc47b36ee4a13a2ea0e1221d91f48e14a53abd74fc85a1d932e8769
vcpkg.json 9349a6da4af53465b06f54e7e3d04b947b34e981cb44c4587b6faf6249175a57
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
